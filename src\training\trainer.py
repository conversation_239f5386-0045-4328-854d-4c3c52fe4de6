"""
Trainer for Integrated LLM with ThinkerModule.
Supports joint training, phased training, and component-specific training.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from typing import Dict, Any, Optional, List, Tuple
import logging
import os
import json
from tqdm import tqdm
import wandb

from ..models.integrated_model import IntegratedLLMWithThinker
from .loss_functions import CombinedLoss


class Trainer:
    """
    Trainer for the integrated LLM with ThinkerModule.

    Supports multiple training strategies:
    1. Joint training: Train all components simultaneously
    2. Phased training: Train components in phases
    3. Component training: Train specific components
    """

    def __init__(self, model: IntegratedLLMWithThinker, config: Dict[str, Any]):
        self.model = model
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Move model to device
        self.model.to(self.device)

        # Handle nested config structure
        if 'training' in config:
            self.training_config = config['training']
        else:
            self.training_config = config

        # Training mode
        self.training_mode = self.training_config.get('training_mode', 'joint')

        # Initialize loss function
        self.loss_fn = CombinedLoss(config)

        # Initialize optimizer
        self.optimizer = self._create_optimizer()

        # Initialize scheduler
        self.scheduler = self._create_scheduler()

        # Training state
        self.current_epoch = 0
        self.global_step = 0
        self.best_loss = float('inf')

        # Logging
        self.logger = logging.getLogger(__name__)
        self.use_wandb = self.training_config.get('use_wandb', False)

        # Checkpointing
        self.save_dir = self.training_config.get('save_dir', 'checkpoints')
        os.makedirs(self.save_dir, exist_ok=True)

    def _create_optimizer(self) -> optim.Optimizer:
        """Create optimizer with component-specific learning rates."""
        training_config = self.training_config

        if self.training_mode == 'component':
            # Different learning rates for different components
            param_groups = [
                {
                    'params': self.model.llm.parameters(),
                    'lr': training_config.get('llm_lr', training_config['learning_rate'])
                },
                {
                    'params': self.model.thinker.parameters(),
                    'lr': training_config.get('thinker_lr', training_config['learning_rate'])
                },
                {
                    'params': self.model.decision_mechanism.parameters(),
                    'lr': training_config.get('decision_lr', training_config['learning_rate'])
                },
                {
                    'params': self.model.projection_layer.parameters(),
                    'lr': training_config.get('projection_lr', training_config['learning_rate'])
                }
            ]

            return optim.AdamW(
                param_groups,
                weight_decay=training_config['weight_decay']
            )
        else:
            # Single learning rate for all parameters
            return optim.AdamW(
                self.model.parameters(),
                lr=training_config['learning_rate'],
                weight_decay=training_config['weight_decay']
            )

    def _create_scheduler(self):
        """Create learning rate scheduler."""
        training_config = self.training_config

        if training_config.get('use_scheduler', True):
            return optim.lr_scheduler.LinearLR(
                self.optimizer,
                start_factor=0.1,
                total_iters=training_config['warmup_steps']
            )
        return None

    def train_epoch(self, train_loader: DataLoader, epoch: int) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()
        total_loss = 0.0
        total_samples = 0

        # Component-specific losses
        component_losses = {
            'llm_loss': 0.0,
            'thinker_loss': 0.0,
            'decision_loss': 0.0,
            'projection_loss': 0.0
        }

        progress_bar = tqdm(train_loader, desc=f'Epoch {epoch}')

        for batch_idx, batch in enumerate(progress_bar):
            # Move batch to device
            input_ids = batch['input_ids'].to(self.device)
            attention_mask = batch.get('attention_mask', None)
            if attention_mask is not None:
                attention_mask = attention_mask.to(self.device)
            labels = batch['labels'].to(self.device)

            # Optional targets
            target_decisions = batch.get('target_decisions', None)
            if target_decisions is not None:
                target_decisions = target_decisions.to(self.device)

            target_reasoning = batch.get('target_reasoning', None)

            # Forward pass
            model_output = self.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels,
                return_reasoning=True
            )

            # Compute losses
            losses = self.loss_fn(
                model_output=model_output,
                labels=labels,
                target_decisions=target_decisions,
                target_reasoning=target_reasoning
            )

            loss = losses['total_loss']

            # Backward pass
            self.optimizer.zero_grad()
            loss.backward()

            # Gradient clipping
            if self.config['training'].get('gradient_clip_norm', 0) > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(),
                    self.config['training']['gradient_clip_norm']
                )

            self.optimizer.step()

            if self.scheduler is not None:
                self.scheduler.step()

            # Update statistics
            batch_size = input_ids.size(0)
            total_loss += loss.item() * batch_size
            total_samples += batch_size

            for key in component_losses:
                if key in losses:
                    component_losses[key] += losses[key].item() * batch_size

            self.global_step += 1

            # Logging
            if self.global_step % self.config['training']['logging_steps'] == 0:
                avg_loss = total_loss / total_samples
                progress_bar.set_postfix({'loss': f'{avg_loss:.4f}'})

                if self.use_wandb:
                    wandb.log({
                        'train/loss': avg_loss,
                        'train/learning_rate': self.optimizer.param_groups[0]['lr'],
                        'train/global_step': self.global_step
                    })

            # Save checkpoint
            if (self.global_step % self.config['training']['save_steps'] == 0 and
                self.global_step > 0):
                self.save_checkpoint(f'checkpoint_step_{self.global_step}')

        # Calculate epoch averages
        epoch_loss = total_loss / total_samples
        for key in component_losses:
            component_losses[key] /= total_samples

        return {
            'total_loss': epoch_loss,
            **component_losses
        }

    def evaluate(self, eval_loader: DataLoader) -> Dict[str, float]:
        """Evaluate the model."""
        self.model.eval()
        total_loss = 0.0
        total_samples = 0

        component_losses = {
            'llm_loss': 0.0,
            'thinker_loss': 0.0,
            'decision_loss': 0.0,
            'projection_loss': 0.0
        }

        with torch.no_grad():
            for batch in tqdm(eval_loader, desc='Evaluating'):
                # Move batch to device
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch.get('attention_mask', None)
                if attention_mask is not None:
                    attention_mask = attention_mask.to(self.device)
                labels = batch['labels'].to(self.device)

                # Forward pass
                model_output = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels,
                    return_reasoning=True
                )

                # Compute losses
                losses = self.loss_fn(
                    model_output=model_output,
                    labels=labels
                )

                loss = losses['total_loss']

                # Update statistics
                batch_size = input_ids.size(0)
                total_loss += loss.item() * batch_size
                total_samples += batch_size

                for key in component_losses:
                    if key in losses:
                        component_losses[key] += losses[key].item() * batch_size

        # Calculate averages
        eval_loss = total_loss / total_samples
        for key in component_losses:
            component_losses[key] /= total_samples

        return {
            'total_loss': eval_loss,
            **component_losses
        }

    def train(self, train_loader: DataLoader,
              eval_loader: Optional[DataLoader] = None,
              num_epochs: Optional[int] = None) -> Dict[str, List[float]]:
        """Main training loop."""
        if num_epochs is None:
            num_epochs = self.config['training']['num_epochs']

        history = {
            'train_loss': [],
            'eval_loss': []
        }

        self.logger.info(f"Starting training for {num_epochs} epochs")
        self.logger.info(f"Training mode: {self.training_mode}")
        self.logger.info(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")

        for epoch in range(num_epochs):
            self.current_epoch = epoch

            # Training
            train_metrics = self.train_epoch(train_loader, epoch)
            history['train_loss'].append(train_metrics['total_loss'])

            self.logger.info(f"Epoch {epoch}: Train Loss = {train_metrics['total_loss']:.4f}")

            # Evaluation
            if eval_loader is not None and epoch % self.config['training']['eval_steps'] == 0:
                eval_metrics = self.evaluate(eval_loader)
                history['eval_loss'].append(eval_metrics['total_loss'])

                self.logger.info(f"Epoch {epoch}: Eval Loss = {eval_metrics['total_loss']:.4f}")

                # Save best model
                if eval_metrics['total_loss'] < self.best_loss:
                    self.best_loss = eval_metrics['total_loss']
                    self.save_checkpoint('best_model')
                    self.logger.info(f"New best model saved with loss: {self.best_loss:.4f}")

                if self.use_wandb:
                    wandb.log({
                        'eval/loss': eval_metrics['total_loss'],
                        'epoch': epoch
                    })

            # Log training metrics
            if self.use_wandb:
                wandb.log({
                    'train/epoch_loss': train_metrics['total_loss'],
                    'epoch': epoch
                })

        self.logger.info("Training completed")
        return history

    def save_checkpoint(self, checkpoint_name: str):
        """Save model checkpoint."""
        checkpoint_path = os.path.join(self.save_dir, f'{checkpoint_name}.pt')

        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'epoch': self.current_epoch,
            'global_step': self.global_step,
            'best_loss': self.best_loss,
            'config': self.config,
            'model_stats': self.model.get_current_stats()
        }

        torch.save(checkpoint, checkpoint_path)
        self.logger.info(f"Checkpoint saved: {checkpoint_path}")

    def load_checkpoint(self, checkpoint_path: str):
        """Load model checkpoint."""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)

        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        if checkpoint['scheduler_state_dict'] and self.scheduler:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

        self.current_epoch = checkpoint['epoch']
        self.global_step = checkpoint['global_step']
        self.best_loss = checkpoint['best_loss']

        self.logger.info(f"Checkpoint loaded: {checkpoint_path}")
        self.logger.info(f"Resumed from epoch {self.current_epoch}, step {self.global_step}")

    def get_training_info(self) -> Dict[str, Any]:
        """Get comprehensive training information."""
        return {
            'current_epoch': self.current_epoch,
            'global_step': self.global_step,
            'best_loss': self.best_loss,
            'training_mode': self.training_mode,
            'model_stats': self.model.get_current_stats(),
            'loss_weights': self.loss_fn.get_loss_weights(),
            'optimizer_info': {
                'type': type(self.optimizer).__name__,
                'learning_rate': self.optimizer.param_groups[0]['lr']
            }
        }
