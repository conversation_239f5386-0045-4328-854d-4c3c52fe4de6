"""
Enhanced Integration Strategy for ThinkerModule + Main LLM
This shows how to properly integrate reasoning with text generation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
from enhanced_thinker_design import EnhancedThinkerModule, EnhancedDecisionMechanism


class ReasoningAwareAttention(nn.Module):
    """Attention mechanism that incorporates reasoning context."""
    
    def __init__(self, d_model: int, num_heads: int = 8):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        
        self.q_proj = nn.Linear(d_model, d_model)
        self.k_proj = nn.Linear(d_model, d_model)
        self.v_proj = nn.Linear(d_model, d_model)
        self.out_proj = nn.Linear(d_model, d_model)
        
        # Reasoning integration
        self.reasoning_gate = nn.Sequential(
            nn.Linear(d_model * 2, d_model),
            nn.<PERSON>g<PERSON><PERSON>()
        )
        
    def forward(self, hidden_states, reasoning_context=None, attention_mask=None):
        batch_size, seq_len = hidden_states.shape[:2]
        
        # Standard attention
        q = self.q_proj(hidden_states)
        k = self.k_proj(hidden_states)
        v = self.v_proj(hidden_states)
        
        # Reshape for multi-head attention
        q = q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Attention scores
        scores = torch.matmul(q, k.transpose(-2, -1)) / (self.head_dim ** 0.5)
        
        if attention_mask is not None:
            scores = scores.masked_fill(attention_mask.unsqueeze(1).unsqueeze(1) == 0, -1e9)
        
        attn_weights = F.softmax(scores, dim=-1)
        attn_output = torch.matmul(attn_weights, v)
        
        # Reshape back
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        attn_output = self.out_proj(attn_output)
        
        # Integrate reasoning context if available
        if reasoning_context is not None:
            # Expand reasoning context to match sequence length
            reasoning_expanded = reasoning_context.unsqueeze(1).expand(-1, seq_len, -1)
            
            # Gated integration
            combined = torch.cat([attn_output, reasoning_expanded], dim=-1)
            gate = self.reasoning_gate(combined)
            attn_output = gate * attn_output + (1 - gate) * reasoning_expanded
        
        return attn_output


class EnhancedLLMWithReasoning(nn.Module):
    """Enhanced LLM that integrates reasoning throughout the generation process."""
    
    def __init__(self, base_llm, thinker_config: Dict[str, Any]):
        super().__init__()
        self.base_llm = base_llm
        self.d_model = base_llm.config.hidden_size
        self.vocab_size = base_llm.config.vocab_size
        
        # Enhanced components
        self.thinker = EnhancedThinkerModule({
            **thinker_config,
            'd_model': self.d_model,
            'vocab_size': self.vocab_size
        })
        
        self.decision_mechanism = EnhancedDecisionMechanism({
            'd_model': self.d_model,
            'vocab_size': self.vocab_size
        })
        
        # Reasoning integration layers
        self.reasoning_projector = nn.Linear(self.d_model, self.d_model)
        self.reasoning_norm = nn.LayerNorm(self.d_model)
        
        # Enhanced attention layers (replace some base LLM layers)
        self.reasoning_aware_layers = nn.ModuleList([
            ReasoningAwareAttention(self.d_model) 
            for _ in range(thinker_config.get('num_integration_layers', 4))
        ])
        
        # Output integration
        self.output_mixer = nn.Sequential(
            nn.Linear(self.d_model * 2, self.d_model),
            nn.ReLU(),
            nn.Linear(self.d_model, self.d_model)
        )
        
    def get_reasoning_context(self, reasoning_output):
        """Extract and process reasoning context for integration."""
        if not reasoning_output.get('used_reasoning', False):
            return None
        
        final_reasoning = reasoning_output.get('final_reasoning_embedding')
        if final_reasoning is None:
            return None
        
        # Pool reasoning steps to single context vector
        reasoning_context = final_reasoning.mean(dim=1)  # Average over steps
        reasoning_context = self.reasoning_projector(reasoning_context)
        reasoning_context = self.reasoning_norm(reasoning_context)
        
        return reasoning_context
    
    def forward(self, input_ids, attention_mask=None, labels=None, return_reasoning=False):
        """Forward pass with integrated reasoning."""
        batch_size = input_ids.size(0)
        device = input_ids.device
        
        # Get input embeddings
        input_embeddings = self.base_llm.get_input_embeddings()(input_ids)
        
        # Decision: Should we use reasoning?
        decision_output = self.decision_mechanism(
            input_ids, attention_mask, input_embeddings
        )
        
        use_reasoning = decision_output['decision_probs'] > 0.5
        
        # Generate reasoning if needed
        reasoning_output = None
        reasoning_context = None
        
        if use_reasoning.any():
            reasoning_output = self.thinker(input_ids, attention_mask)
            reasoning_context = self.get_reasoning_context(reasoning_output)
        
        # Forward through base LLM with reasoning integration
        hidden_states = input_embeddings
        
        # Use base LLM transformer layers
        for i, layer in enumerate(self.base_llm.transformer.h):
            # Apply base layer
            layer_output = layer(hidden_states, attention_mask=attention_mask)
            if isinstance(layer_output, tuple):
                hidden_states = layer_output[0]
            else:
                hidden_states = layer_output
            
            # Apply reasoning-aware attention for some layers
            if i < len(self.reasoning_aware_layers) and reasoning_context is not None:
                reasoning_enhanced = self.reasoning_aware_layers[i](
                    hidden_states, reasoning_context, attention_mask
                )
                # Residual connection
                hidden_states = hidden_states + reasoning_enhanced
        
        # Final layer norm
        hidden_states = self.base_llm.transformer.ln_f(hidden_states)
        
        # Output projection
        if reasoning_context is not None:
            # Mix reasoning context with final hidden states
            reasoning_expanded = reasoning_context.unsqueeze(1).expand_as(hidden_states)
            mixed_states = torch.cat([hidden_states, reasoning_expanded], dim=-1)
            hidden_states = self.output_mixer(mixed_states)
        
        # Get logits
        logits = self.base_llm.lm_head(hidden_states)
        
        # Calculate loss if labels provided
        loss = None
        if labels is not None:
            shift_logits = logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()
            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(shift_logits.view(-1, shift_logits.size(-1)), shift_labels.view(-1))
        
        output = {
            'logits': logits,
            'loss': loss,
            'decision_info': decision_output,
            'used_reasoning': use_reasoning,
        }
        
        if return_reasoning and reasoning_output is not None:
            output['reasoning_outputs'] = [reasoning_output]
        
        return output
    
    def generate(self, input_ids, max_new_tokens=50, temperature=1.0, top_p=0.9, 
                 force_thinking=None, return_reasoning=False, **kwargs):
        """Enhanced generation with reasoning integration."""
        self.eval()
        device = input_ids.device
        batch_size = input_ids.size(0)
        
        # Initial reasoning phase
        with torch.no_grad():
            # Get input embeddings for decision
            input_embeddings = self.base_llm.get_input_embeddings()(input_ids)
            
            # Make reasoning decision
            if force_thinking is None:
                decision_output = self.decision_mechanism(input_ids, None, input_embeddings)
                use_thinking = decision_output['decision_probs'] > 0.5
            else:
                use_thinking = torch.tensor([force_thinking] * batch_size, device=device)
                decision_output = {'decision_probs': use_thinking.float(), 'confidence': torch.ones_like(use_thinking)}
            
            # Generate reasoning if needed
            reasoning_output = None
            reasoning_context = None
            
            if use_thinking.any():
                reasoning_output = self.thinker(input_ids)
                reasoning_context = self.get_reasoning_context(reasoning_output)
            
            # Generation loop
            generated_ids = input_ids.clone()
            
            for _ in range(max_new_tokens):
                # Forward pass
                outputs = self.forward(generated_ids, return_reasoning=False)
                logits = outputs['logits']
                
                # Get next token logits
                next_token_logits = logits[:, -1, :] / temperature
                
                # Apply top-p filtering
                if top_p < 1.0:
                    sorted_logits, sorted_indices = torch.sort(next_token_logits, descending=True)
                    cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)
                    sorted_indices_to_remove = cumulative_probs > top_p
                    sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
                    sorted_indices_to_remove[..., 0] = 0
                    
                    indices_to_remove = sorted_indices_to_remove.scatter(1, sorted_indices, sorted_indices_to_remove)
                    next_token_logits[indices_to_remove] = -float('inf')
                
                # Sample next token
                probs = F.softmax(next_token_logits, dim=-1)
                next_token = torch.multinomial(probs, num_samples=1)
                
                # Append to generated sequence
                generated_ids = torch.cat([generated_ids, next_token], dim=-1)
                
                # Check for EOS token
                if next_token.item() == self.base_llm.config.eos_token_id:
                    break
        
        result = {
            'generated_ids': generated_ids,
            'thinking_used': use_thinking,
            'decision_info': decision_output
        }
        
        if return_reasoning and reasoning_output is not None:
            result['reasoning_info'] = reasoning_output
        
        return result


class AdvancedTrainingStrategy:
    """Advanced training strategy for the enhanced model."""
    
    @staticmethod
    def create_curriculum_data(base_data, difficulty_levels=5):
        """Create curriculum learning data with increasing difficulty."""
        curriculum = {i: [] for i in range(difficulty_levels)}
        
        for item in base_data:
            # Analyze difficulty (this is a simplified example)
            text = item.get('text', '')
            
            # Simple heuristics for difficulty
            length_score = min(len(text.split()) / 100, 1.0)  # Normalize by 100 words
            complexity_score = len(set(text.split())) / len(text.split()) if text else 0
            
            difficulty = int((length_score + complexity_score) / 2 * (difficulty_levels - 1))
            curriculum[difficulty].append(item)
        
        return curriculum
    
    @staticmethod
    def progressive_loss_weights(epoch, total_epochs):
        """Progressive loss weighting strategy."""
        progress = epoch / total_epochs
        
        # Start with high LLM weight, gradually increase reasoning weight
        llm_weight = 1.0
        thinker_weight = 0.05 + 0.15 * progress  # 0.05 -> 0.2
        decision_weight = 0.1 + 0.1 * progress   # 0.1 -> 0.2
        
        return {
            'llm_loss_weight': llm_weight,
            'thinker_loss_weight': thinker_weight,
            'decision_loss_weight': decision_weight
        }
    
    @staticmethod
    def quality_based_sampling(reasoning_outputs, quality_threshold=0.6):
        """Sample high-quality reasoning examples for training."""
        high_quality_examples = []
        
        for output in reasoning_outputs:
            quality_scores = output.get('quality_scores', [])
            if quality_scores:
                avg_quality = sum(q.item() for q in quality_scores) / len(quality_scores)
                if avg_quality > quality_threshold:
                    high_quality_examples.append(output)
        
        return high_quality_examples
