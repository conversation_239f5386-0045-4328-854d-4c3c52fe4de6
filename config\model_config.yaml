# LLM with ThinkerModule Configuration

model:
  # Main LLM Configuration
  llm:
    vocab_size: 50257
    hidden_size: 768
    num_layers: 12
    num_attention_heads: 12
    intermediate_size: 3072
    max_position_embeddings: 2048
    dropout: 0.1
    layer_norm_eps: 1e-5

  # ThinkerModule Configuration
  thinker:
    hidden_size: 512
    num_layers: 6
    num_attention_heads: 8
    intermediate_size: 2048
    max_reasoning_steps: 8  # Safety limit for dynamic generation
    dropout: 0.1
    layer_norm_eps: 1e-5
    stop_threshold: 0.7  # Threshold for stopping reasoning generation

  # Decision Mechanism Configuration
  decision:
    hidden_size: 256
    num_layers: 2
    threshold: 0.5
    complexity_features: 4

  # Projection Layer Configuration
  projection:
    input_size: 512  # thinker hidden_size
    output_size: 768  # llm hidden_size
    num_layers: 2
    dropout: 0.1

training:
  batch_size: 2  # Reduced for dynamic reasoning
  learning_rate: 5e-5
  num_epochs: 3  # Reduced for testing
  warmup_steps: 1000
  weight_decay: 0.01
  gradient_clip_norm: 1.0
  save_steps: 1000
  eval_steps: 500
  logging_steps: 100

  # Loss weights - Rebalanced for better training
  llm_loss_weight: 1.0
  thinker_loss_weight: 0.1  # Reduced to prevent overwhelming main LLM loss
  decision_loss_weight: 0.2
  projection_loss_weight: 0.05

  # Adaptive loss weighting
  adaptive_loss_weights: false  # Start with fixed weights

data:
  max_length: 1024
  train_file: "data/train.jsonl"
  eval_file: "data/eval.jsonl"
  test_file: "data/test.jsonl"

inference:
  max_new_tokens: 512
  temperature: 0.7
  top_p: 0.9
  do_sample: true
  num_beams: 1
