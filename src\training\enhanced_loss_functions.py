"""
Enhanced loss functions for the improved ThinkerModule architecture.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any


class EnhancedThinkerLoss(nn.Module):
    """Enhanced loss function for ThinkerModule reasoning steps."""
    
    def __init__(self, vocab_size: int):
        super().__init__()
        self.vocab_size = vocab_size
        self.loss_fn = nn.CrossEntropyLoss(ignore_index=-100)
    
    def forward(self, reasoning_output: Dict[str, Any],
                target_reasoning: Optional[List[torch.Tensor]] = None,
                target_num_steps: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Compute enhanced loss for dynamic reasoning steps.

        Args:
            reasoning_output: Output from ThinkerModule containing reasoning steps and stop probabilities
            target_reasoning: Target reasoning steps (optional)
            target_num_steps: Target number of reasoning steps (optional)

        Returns:
            Combined reasoning loss
        """
        reasoning_steps = reasoning_output.get('reasoning_steps', [])
        stop_probabilities = reasoning_output.get('stop_probabilities', [])
        quality_scores = reasoning_output.get('quality_scores', [])

        if not reasoning_steps:
            return torch.tensor(0.0, device=next(iter(reasoning_output.values())).device)

        device = reasoning_steps[0].device

        # Enhanced reasoning step loss
        if target_reasoning is None:
            # Multi-component self-supervised loss
            step_loss = 0.0
            
            # 1. Quality-weighted consistency loss
            if len(reasoning_steps) > 1 and quality_scores:
                consistency_loss = 0.0
                for i in range(len(reasoning_steps) - 1):
                    current_probs = F.softmax(reasoning_steps[i], dim=-1)
                    next_probs = F.softmax(reasoning_steps[i + 1], dim=-1)
                    
                    # Weight by quality scores
                    quality_weight = (quality_scores[i] + quality_scores[i + 1]) / 2
                    
                    # KL divergence for consistency
                    kl_div = F.kl_div(torch.log(next_probs + 1e-8), current_probs, reduction='batchmean')
                    consistency_loss += quality_weight * kl_div
                
                consistency_loss = consistency_loss / (len(reasoning_steps) - 1)
                step_loss += 0.1 * consistency_loss
            
            # 2. Progressive complexity loss: later steps should be more focused
            complexity_loss = 0.0
            for i, step_logits in enumerate(reasoning_steps):
                probs = F.softmax(step_logits, dim=-1)
                entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
                
                # Target entropy decreases with step number (more focused reasoning)
                target_entropy = 4.0 - (i * 0.3)  # Start at 4.0, decrease by 0.3 per step
                target_entropy = max(target_entropy, 1.5)  # Minimum entropy
                
                entropy_loss = F.mse_loss(entropy, torch.full_like(entropy, target_entropy))
                complexity_loss += entropy_loss
            
            complexity_loss = complexity_loss / len(reasoning_steps)
            step_loss += 0.3 * complexity_loss
            
            # 3. Quality-based regularization
            if quality_scores:
                quality_loss = 0.0
                for i, (step_logits, quality) in enumerate(zip(reasoning_steps, quality_scores)):
                    # Encourage high-quality steps to have lower perplexity
                    probs = F.softmax(step_logits, dim=-1)
                    perplexity = torch.exp(-torch.sum(probs * torch.log(probs + 1e-8), dim=-1))
                    
                    # Quality should be inversely related to perplexity
                    quality_target = 1.0 / (1.0 + perplexity.mean())
                    quality_loss += F.mse_loss(quality, quality_target.expand_as(quality))
                
                quality_loss = quality_loss / len(quality_scores)
                step_loss += 0.2 * quality_loss
            
            # 4. Anti-repetition loss: discourage identical consecutive tokens
            repetition_loss = 0.0
            for step_logits in reasoning_steps:
                probs = F.softmax(step_logits, dim=-1)
                # Encourage diversity in top tokens
                top_probs, _ = torch.topk(probs, k=5, dim=-1)
                # Penalize if top probabilities are too similar
                prob_variance = torch.var(top_probs, dim=-1)
                repetition_loss += torch.mean(1.0 / (prob_variance + 1e-6))
            
            repetition_loss = repetition_loss / len(reasoning_steps)
            step_loss += 0.1 * repetition_loss
            
        else:
            # Supervised loss with target reasoning (simplified for now)
            step_loss = torch.tensor(0.0, device=device)
            # TODO: Implement proper supervised loss when we have good training data

        # Stop probability loss
        stop_loss = 0.0
        if stop_probabilities and target_num_steps is not None:
            for i, stop_prob in enumerate(stop_probabilities):
                # Target: should stop after target_num_steps
                target_stop = 1.0 if i >= target_num_steps else 0.0
                target_tensor = torch.full_like(stop_prob, target_stop)
                stop_loss += F.binary_cross_entropy(stop_prob, target_tensor)
            stop_loss = stop_loss / len(stop_probabilities)
        elif stop_probabilities:
            # Encourage reasonable stopping behavior
            for i, stop_prob in enumerate(stop_probabilities):
                # Gradually increase stopping probability
                target_stop = min(0.1 + i * 0.1, 0.8)
                target_tensor = torch.full_like(stop_prob, target_stop)
                stop_loss += F.binary_cross_entropy(stop_prob, target_tensor)
            stop_loss = stop_loss / len(stop_probabilities)

        return step_loss + 0.1 * stop_loss


class EnhancedDecisionLoss(nn.Module):
    """Enhanced loss function for decision mechanism."""
    
    def __init__(self):
        super().__init__()
    
    def forward(self, decision_logits: torch.Tensor, 
                target_decisions: Optional[torch.Tensor] = None,
                complexity_scores: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Compute decision loss with complexity awareness.
        
        Args:
            decision_logits: Raw decision logits from decision mechanism
            target_decisions: Target decisions (optional)
            complexity_scores: Complexity scores from input analysis
            
        Returns:
            Decision loss
        """
        if target_decisions is not None:
            # Supervised decision loss
            decision_loss = F.binary_cross_entropy_with_logits(
                decision_logits.squeeze(-1), target_decisions.float()
            )
        else:
            # Enhanced unsupervised decision loss
            decision_probs = torch.sigmoid(decision_logits.squeeze(-1))
            
            if complexity_scores is not None:
                # Use complexity scores as pseudo-targets
                # High complexity → should use reasoning (target = 1)
                # Low complexity → should use direct generation (target = 0)
                pseudo_targets = complexity_scores.squeeze(-1)
                decision_loss = F.mse_loss(decision_probs, pseudo_targets)
            else:
                # Encourage balanced decisions with slight bias toward reasoning
                target_mean = 0.6  # Slight bias toward using reasoning
                decision_loss = F.mse_loss(decision_probs.mean(),
                                         torch.tensor(target_mean, device=decision_probs.device))
        
        return decision_loss


class EnhancedCombinedLoss(nn.Module):
    """Enhanced combined loss function for the integrated model."""
    
    def __init__(self, vocab_size: int, config: Dict[str, Any]):
        super().__init__()
        self.llm_loss = nn.CrossEntropyLoss()
        self.thinker_loss = EnhancedThinkerLoss(vocab_size)
        self.decision_loss = EnhancedDecisionLoss()
        
        # Loss weights with progressive scheduling
        self.llm_loss_weight = config.get('llm_loss_weight', 1.0)
        self.thinker_loss_weight = config.get('thinker_loss_weight', 0.1)
        self.decision_loss_weight = config.get('decision_loss_weight', 0.2)
        self.projection_loss_weight = config.get('projection_loss_weight', 0.05)
        
        # Progressive weighting
        self.use_progressive_weights = config.get('use_progressive_weights', True)
        self.current_epoch = 0
        self.total_epochs = config.get('num_epochs', 10)
    
    def update_epoch(self, epoch: int):
        """Update current epoch for progressive weighting."""
        self.current_epoch = epoch
        
        if self.use_progressive_weights:
            # Progressive loss weighting
            progress = epoch / self.total_epochs
            
            # Start with high LLM weight, gradually increase reasoning weight
            self.llm_loss_weight = 1.0
            self.thinker_loss_weight = 0.05 + 0.15 * progress  # 0.05 → 0.2
            self.decision_loss_weight = 0.1 + 0.1 * progress   # 0.1 → 0.2
    
    def forward(self, model_output: Dict[str, Any], labels: torch.Tensor,
                target_decisions: Optional[torch.Tensor] = None,
                target_reasoning: Optional[List[torch.Tensor]] = None) -> Dict[str, torch.Tensor]:
        """
        Compute combined loss for all model components.
        
        Args:
            model_output: Output from the integrated model
            labels: Target labels for language modeling
            target_decisions: Target decisions for decision mechanism
            target_reasoning: Target reasoning steps
            
        Returns:
            Dictionary of individual and combined losses
        """
        losses = {}
        device = labels.device
        
        # LLM loss
        if 'loss' in model_output and model_output['loss'] is not None:
            losses['llm_loss'] = model_output['loss']
        else:
            losses['llm_loss'] = self.llm_loss(model_output['logits'], labels)
        
        # ThinkerModule loss (if reasoning outputs available)
        if 'reasoning_outputs' in model_output:
            thinker_losses = []
            for reasoning_output in model_output['reasoning_outputs']:
                # Use self-supervised loss for now
                thinker_loss = self.thinker_loss(reasoning_output, target_reasoning=None)
                thinker_losses.append(thinker_loss)
            
            if thinker_losses:
                losses['thinker_loss'] = torch.stack(thinker_losses).mean()
            else:
                losses['thinker_loss'] = torch.tensor(0.0, device=device)
        else:
            losses['thinker_loss'] = torch.tensor(0.0, device=device)
        
        # Decision loss
        decision_info = model_output['decision_info']
        if 'decision_probs' in decision_info:
            # Convert probabilities back to logits for loss calculation
            decision_probs = decision_info['decision_probs']
            decision_logits = torch.log(decision_probs / (1 - decision_probs + 1e-8))
            
            # Get complexity scores if available
            complexity_scores = None
            if 'reasoning_outputs' in model_output:
                for reasoning_output in model_output['reasoning_outputs']:
                    if 'complexity_info' in reasoning_output:
                        complexity_scores = reasoning_output['complexity_info']['complexity_score']
                        break
            
            losses['decision_loss'] = self.decision_loss(
                decision_logits, target_decisions, complexity_scores
            )
        else:
            losses['decision_loss'] = torch.tensor(0.0, device=device)
        
        # Projection loss (regularization for reasoning integration)
        losses['projection_loss'] = torch.tensor(0.0, device=device)
        
        # Combined loss with current weights
        losses['total_loss'] = (
            self.llm_loss_weight * losses['llm_loss'] +
            self.thinker_loss_weight * losses['thinker_loss'] +
            self.decision_loss_weight * losses['decision_loss'] +
            self.projection_loss_weight * losses['projection_loss']
        )
        
        return losses
