{"text": "The capital of France is Paris. It is known for the Eiffel Tower.", "needs_thinking": false}
{"text": "To solve the equation 2x + 5 = 13, we need to isolate x. First, subtract 5 from both sides: 2x = 8. Then divide by 2: x = 4.", "needs_thinking": true, "reasoning": ["Start with equation 2x + 5 = 13", "Subtract 5 from both sides", "Get 2x = 8", "Divide both sides by 2", "Final answer: x = 4"]}
{"text": "Tokyo is the capital of Japan.", "needs_thinking": false}
{"text": "If you invest $1000 at 8% annual interest for 2 years, how much interest will you earn?", "needs_thinking": true, "reasoning": ["Principal = $1000", "Rate = 8% = 0.08", "Time = 2 years", "Simple interest = Principal × Rate × Time", "Calculate: 1000 × 0.08 × 2 = $160", "Answer: $160 in interest"]}
{"text": "Oxygen is essential for human survival.", "needs_thinking": false}
{"text": "A pizza is cut into 8 equal slices. If 3 people want to share it equally, how many slices should each person get?", "needs_thinking": true, "reasoning": ["Total slices = 8", "Number of people = 3", "Divide slices by people: 8 ÷ 3 = 2.67", "Cannot give partial slices exactly", "Answer: 2 slices each with 2 slices remaining"]}
{"text": "Saturn has rings around it.", "needs_thinking": false}
{"text": "If a square has a perimeter of 20 cm, what is its area?", "needs_thinking": true, "reasoning": ["Recall: perimeter = 4 × side length", "20 = 4 × side length", "Solve for side: 20 ÷ 4 = 5 cm", "Area = side × side = 5 × 5", "Answer: 25 square centimeters"]}
{"text": "Mercury is the smallest planet in our solar system.", "needs_thinking": false}
{"text": "If it takes 8 hours to read a 400-page book, how long would it take to read a 300-page book at the same rate?", "needs_thinking": true, "reasoning": ["Find pages per hour: 400 ÷ 8 = 50 pages/hour", "For 300 pages divide by rate: 300 ÷ 50", "Calculate: 300 ÷ 50 = 6", "Answer: 6 hours"]}
{"text": "The Great Wall of China is visible from space.", "needs_thinking": false}
{"text": "What is the sum of the first five prime numbers?", "needs_thinking": true, "reasoning": ["List first five primes: 2, 3, 5, 7, 11", "Add them: 2 + 3 + 5 + 7 + 11", "Calculate: 28", "Answer: 28"]}
{"text": "The capital of Germany is Berlin.", "needs_thinking": false}
{"text": "If a car travels at 50 mph for 3 hours, how far does it go?", "needs_thinking": true, "reasoning": ["Distance = speed × time", "Substitute values: speed = 50 mph, time = 3 hours", "Calculate: 50 × 3 = 150", "Answer: 150 miles"]}
{"text": "The human brain weighs about 3 pounds.", "needs_thinking": false}
{"text": "If a shirt costs $25 and is on sale for 10% off, what is the sale price?", "needs_thinking": true, "reasoning": ["Calculate discount amount: 10% of $25 = 0.10 × 25 = $2.50", "Subtract discount from original price: $25 - $2.50 = $22.50", "Answer: $22.50"]}
{"text": "The largest ocean on Earth is the Pacific Ocean.", "needs_thinking": false}
{"text": "If a cube has a side length of 4 cm, what is its volume and surface area?", "needs_thinking": true, "reasoning": ["Volume = side^3 = 4^3 = 64 cubic cm", "Surface Area = 6 × side^2 = 6 × 4^2 = 6 × 16 = 96 square cm", "Answer: Volume = 64 cubic cm, Surface Area = 96 square cm"]}
{"text": "The chemical symbol for gold is Au.", "needs_thinking": false}
{"text": "If you have 20 candies and eat 1/4 of them, how many are left?", "needs_thinking": true, "reasoning": ["Calculate eaten candies: 1/4 of 20 = 5", "Subtract eaten from total: 20 - 5 = 15", "Answer: 15 candies left"]}
{"text": "The capital of Spain is Madrid.", "needs_thinking": false}
{"text": "If a sequence is 1, 3, 5, 7, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: each number increases by 2", "Last number is 7", "Add 2 to 7", "Answer: 9"]}
