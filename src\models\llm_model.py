"""
Main LLM Model - Transformer-based autoregressive language model.
This component handles standard text generation tasks and can operate independently.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
# from transformers import GPT2Config, GPT2LMHeadModel  # For future reference
from typing import Optional, Tuple, Dict, Any
import math


class RotaryPositionalEmbedding(nn.Module):
    """Rotary Positional Embedding for improved position encoding."""

    def __init__(self, dim: int, max_seq_len: int = 2048):
        super().__init__()
        self.dim = dim
        self.max_seq_len = max_seq_len

        # Precompute frequencies
        inv_freq = 1.0 / (10000 ** (torch.arange(0, dim, 2).float() / dim))
        self.register_buffer('inv_freq', inv_freq)

    def forward(self, x: torch.Tensor, seq_len: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """Apply rotary positional embedding."""
        t = torch.arange(seq_len, device=x.device).type_as(self.inv_freq)
        freqs = torch.einsum('i,j->ij', t, self.inv_freq)
        emb = torch.cat((freqs, freqs), dim=-1)
        cos_emb = emb.cos()
        sin_emb = emb.sin()
        return cos_emb, sin_emb


class MultiHeadAttention(nn.Module):
    """Enhanced Multi-Head Attention with optional rotary embeddings."""

    def __init__(self, hidden_size: int, num_heads: int, dropout: float = 0.1,
                 use_rotary: bool = True, max_seq_len: int = 2048):
        super().__init__()
        assert hidden_size % num_heads == 0

        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.head_dim = hidden_size // num_heads
        self.scale = 1.0 / math.sqrt(self.head_dim)

        self.q_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.k_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.v_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.out_proj = nn.Linear(hidden_size, hidden_size)

        self.dropout = nn.Dropout(dropout)

        self.use_rotary = use_rotary
        if use_rotary:
            self.rotary_emb = RotaryPositionalEmbedding(self.head_dim, max_seq_len)

    def apply_rotary_emb(self, x: torch.Tensor, cos: torch.Tensor, sin: torch.Tensor) -> torch.Tensor:
        """Apply rotary embedding to input tensor."""
        # Ensure even dimensions for rotary embedding
        if x.size(-1) % 2 != 0:
            # If odd dimension, just return original tensor
            return x

        x1, x2 = x[..., ::2], x[..., 1::2]

        # Ensure cos and sin have the right shape
        cos = cos[..., :x1.size(-1)]
        sin = sin[..., :x1.size(-1)]

        return torch.cat([x1 * cos - x2 * sin, x1 * sin + x2 * cos], dim=-1)

    def forward(self, hidden_states: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                is_causal: bool = True) -> torch.Tensor:
        """Forward pass of multi-head attention."""
        batch_size, seq_len, _ = hidden_states.shape

        # Project to Q, K, V
        q = self.q_proj(hidden_states)
        k = self.k_proj(hidden_states)
        v = self.v_proj(hidden_states)

        # Reshape for multi-head attention
        q = q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)

        # Apply rotary embeddings if enabled
        if self.use_rotary:
            cos_emb, sin_emb = self.rotary_emb(q, seq_len)
            q = self.apply_rotary_emb(q, cos_emb, sin_emb)
            k = self.apply_rotary_emb(k, cos_emb, sin_emb)

        # Scaled dot-product attention
        attn_weights = torch.matmul(q, k.transpose(-2, -1)) * self.scale

        # Apply causal mask for autoregressive generation
        if is_causal:
            causal_mask = torch.triu(torch.ones(seq_len, seq_len, device=q.device), diagonal=1).bool()
            attn_weights.masked_fill_(causal_mask, float('-inf'))

        # Apply attention mask if provided
        if attention_mask is not None:
            # Convert attention mask to additive mask for attention weights
            # attention_mask: [batch_size, seq_len] -> [batch_size, 1, 1, seq_len]
            mask = (1.0 - attention_mask.unsqueeze(1).unsqueeze(1)) * -10000.0
            attn_weights = attn_weights + mask

        attn_weights = F.softmax(attn_weights, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # Apply attention to values
        attn_output = torch.matmul(attn_weights, v)
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.hidden_size
        )

        return self.out_proj(attn_output)


class TransformerBlock(nn.Module):
    """Transformer block with attention and feed-forward layers."""

    def __init__(self, hidden_size: int, num_heads: int, intermediate_size: int,
                 dropout: float = 0.1, layer_norm_eps: float = 1e-5):
        super().__init__()

        self.attention = MultiHeadAttention(hidden_size, num_heads, dropout, use_rotary=False)
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_size, intermediate_size),
            nn.GELU(),
            nn.Linear(intermediate_size, hidden_size),
            nn.Dropout(dropout)
        )

        self.ln1 = nn.LayerNorm(hidden_size, eps=layer_norm_eps)
        self.ln2 = nn.LayerNorm(hidden_size, eps=layer_norm_eps)
        self.dropout = nn.Dropout(dropout)

    def forward(self, hidden_states: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass with residual connections."""
        # Self-attention with residual connection
        residual = hidden_states
        hidden_states = self.ln1(hidden_states)
        hidden_states = self.attention(hidden_states, attention_mask)
        hidden_states = self.dropout(hidden_states) + residual

        # Feed-forward with residual connection
        residual = hidden_states
        hidden_states = self.ln2(hidden_states)
        hidden_states = self.feed_forward(hidden_states)
        hidden_states = hidden_states + residual

        return hidden_states


class LLMModel(nn.Module):
    """
    Main autoregressive LLM model for text generation.
    Can operate independently or receive enhanced hidden states from ThinkerModule.
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config

        # Model dimensions
        self.vocab_size = config['vocab_size']
        self.hidden_size = config['hidden_size']
        self.num_layers = config['num_layers']
        self.num_heads = config['num_attention_heads']
        self.max_position_embeddings = config['max_position_embeddings']

        # Embeddings
        self.token_embedding = nn.Embedding(self.vocab_size, self.hidden_size)
        self.position_embedding = nn.Embedding(self.max_position_embeddings, self.hidden_size)

        # Transformer layers
        self.layers = nn.ModuleList([
            TransformerBlock(
                hidden_size=self.hidden_size,
                num_heads=self.num_heads,
                intermediate_size=config['intermediate_size'],
                dropout=config['dropout'],
                layer_norm_eps=config['layer_norm_eps']
            ) for _ in range(self.num_layers)
        ])

        # Final layer norm and output projection
        self.ln_f = nn.LayerNorm(self.hidden_size, eps=config['layer_norm_eps'])
        self.lm_head = nn.Linear(self.hidden_size, self.vocab_size, bias=False)

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)

    def forward(self, input_ids: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                enhanced_hidden_states: Optional[torch.Tensor] = None,
                labels: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass of the LLM model.

        Args:
            input_ids: Token IDs [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
            enhanced_hidden_states: Hidden states from ThinkerModule [batch_size, seq_len, hidden_size]
            labels: Target labels for training [batch_size, seq_len]

        Returns:
            Dictionary containing logits, loss, and hidden states
        """
        batch_size, seq_len = input_ids.shape
        device = input_ids.device

        # Create position IDs
        position_ids = torch.arange(seq_len, device=device).unsqueeze(0).expand(batch_size, -1)

        # Embeddings
        token_embeds = self.token_embedding(input_ids)
        position_embeds = self.position_embedding(position_ids)
        hidden_states = token_embeds + position_embeds

        # Integrate enhanced hidden states from ThinkerModule if provided
        if enhanced_hidden_states is not None:
            # Ensure sequence lengths match
            if enhanced_hidden_states.shape[1] == hidden_states.shape[1]:
                # Simple addition - could be more sophisticated (e.g., gated combination)
                hidden_states = hidden_states + enhanced_hidden_states
            else:
                # If sequence lengths don't match, only use the overlapping part
                min_seq_len = min(enhanced_hidden_states.shape[1], hidden_states.shape[1])
                hidden_states[:, :min_seq_len, :] = (
                    hidden_states[:, :min_seq_len, :] + enhanced_hidden_states[:, :min_seq_len, :]
                )

        # Apply transformer layers
        for layer in self.layers:
            hidden_states = layer(hidden_states, attention_mask)

        # Final layer norm
        hidden_states = self.ln_f(hidden_states)

        # Output projection
        logits = self.lm_head(hidden_states)

        # Calculate loss if labels provided
        loss = None
        if labels is not None:
            # Shift labels for next token prediction
            shift_logits = logits[..., :-1, :].contiguous()
            shift_labels = labels[..., 1:].contiguous()

            loss_fct = nn.CrossEntropyLoss()
            loss = loss_fct(shift_logits.view(-1, self.vocab_size), shift_labels.view(-1))

        return {
            'logits': logits,
            'loss': loss,
            'hidden_states': hidden_states
        }

    def generate(self, input_ids: torch.Tensor, max_new_tokens: int = 50,
                 temperature: float = 1.0, top_p: float = 0.9,
                 enhanced_hidden_states: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Generate text autoregressively."""
        self.eval()

        with torch.no_grad():
            for _ in range(max_new_tokens):
                # Forward pass
                outputs = self.forward(input_ids, enhanced_hidden_states=enhanced_hidden_states)
                logits = outputs['logits']

                # Get next token logits
                next_token_logits = logits[:, -1, :] / temperature

                # Apply top-p sampling
                if top_p < 1.0:
                    sorted_logits, sorted_indices = torch.sort(next_token_logits, descending=True)
                    cumulative_probs = torch.cumsum(F.softmax(sorted_logits, dim=-1), dim=-1)

                    # Remove tokens with cumulative probability above the threshold
                    sorted_indices_to_remove = cumulative_probs > top_p
                    sorted_indices_to_remove[..., 1:] = sorted_indices_to_remove[..., :-1].clone()
                    sorted_indices_to_remove[..., 0] = 0

                    indices_to_remove = sorted_indices_to_remove.scatter(1, sorted_indices, sorted_indices_to_remove)
                    next_token_logits[indices_to_remove] = float('-inf')

                # Sample next token
                probs = F.softmax(next_token_logits, dim=-1)
                next_token = torch.multinomial(probs, num_samples=1)

                # Append to input
                input_ids = torch.cat([input_ids, next_token], dim=-1)

        return input_ids
