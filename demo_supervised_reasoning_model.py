"""
Demo script for the supervised reasoning model with all improvements.
This tests the enhanced reasoning capabilities with proper supervision.
"""

import os
import sys
import torch
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from transformers import AutoTokenizer
from src.models.enhanced_integrated_model import EnhancedIntegratedLL<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def create_demo_config():
    """Create configuration for demo."""
    return {
        'model': {
            'thinker': {
                'max_reasoning_steps': 8,
                'num_reasoning_layers': 3,
                'd_model': 768,
                'vocab_size': 50257
            },
            'num_integration_layers': 2
        }
    }


def load_supervised_model(checkpoint_path: str, config: dict, logger):
    """Load supervised model from checkpoint."""
    model = EnhancedIntegratedLL<PERSON><PERSON>ithT<PERSON>ker(config)

    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"Supervised model loaded from {checkpoint_path}")
        logger.info(f"Training epoch: {checkpoint.get('epoch', 'unknown')}")
        logger.info(f"Training step: {checkpoint.get('global_step', 'unknown')}")
        logger.info(f"Best loss: {checkpoint.get('best_loss', 'unknown')}")
    else:
        logger.warning(f"Checkpoint not found: {checkpoint_path}")
        logger.warning("Using randomly initialized model")

    return model


def test_supervised_reasoning(model, tokenizer, prompts, logger):
    """Test the supervised reasoning capabilities."""
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    logger.info("Testing supervised reasoning capabilities...")
    
    for i, prompt_info in enumerate(prompts):
        prompt = prompt_info['prompt']
        expected_complexity = prompt_info['complexity']
        category = prompt_info['category']
        
        logger.info(f"\n{'='*80}")
        logger.info(f"Test {i+1}: {prompt}")
        logger.info(f"Expected: {expected_complexity} complexity, {category} category")
        logger.info(f"{'='*80}")
        
        # Tokenize input
        input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)
        
        # Test with forced reasoning
        logger.info("\nSUPERVISED REASONING MODE:")
        with torch.no_grad():
            result = model.generate(
                input_ids=input_ids,
                max_new_tokens=100,
                temperature=0.7,
                top_p=0.9,
                force_thinking=True,
                return_reasoning=True,
                tokenizer=tokenizer
            )
        
        # Decode generated text
        generated_text = tokenizer.decode(
            result['generated_ids'][0],
            skip_special_tokens=True
        )
        
        logger.info(f"Generated: {generated_text[len(prompt):]}")
        logger.info(f"Used thinking: {result['thinking_used'][0].item()}")
        
        # Analyze reasoning if available
        if 'reasoning_info' in result and result['reasoning_info']:
            reasoning_info = result['reasoning_info']
            
            if reasoning_info.get('used_reasoning', False):
                complexity_info = reasoning_info['complexity_info']
                logger.info(f"Complexity score: {complexity_info['complexity_score'].item():.3f}")
                logger.info(f"Suggested steps: {complexity_info['suggested_steps']}")
                
                # Show reasoning steps
                reasoning_steps = reasoning_info.get('reasoning_steps', [])
                quality_scores = reasoning_info.get('quality_scores', [])
                stop_probs = reasoning_info.get('stop_probabilities', [])
                
                logger.info(f"Generated {len(reasoning_steps)} reasoning steps:")
                
                for j, step_logits in enumerate(reasoning_steps):
                    # Sample from step logits to show reasoning
                    step_probs = torch.softmax(step_logits[0, 0] / 0.8, dim=-1)
                    top_tokens = torch.topk(step_probs, k=5)
                    
                    step_tokens = [tokenizer.decode([token_id]) for token_id in top_tokens.indices]
                    step_probs_list = [f"{prob:.3f}" for prob in top_tokens.values]
                    
                    quality = quality_scores[j].item() if j < len(quality_scores) else 0.0
                    stop_prob = stop_probs[j].item() if j < len(stop_probs) else 0.0
                    
                    logger.info(f"  Step {j+1}: {step_tokens} (quality: {quality:.3f}, stop: {stop_prob:.3f})")
            else:
                logger.info("Reasoning was skipped (low complexity)")
        
        # Test with automatic decision
        logger.info("\nAUTOMATIC DECISION MODE:")
        with torch.no_grad():
            result = model.generate(
                input_ids=input_ids,
                max_new_tokens=100,
                temperature=0.7,
                top_p=0.9,
                force_thinking=None,
                return_reasoning=False,
                tokenizer=tokenizer
            )
        
        generated_text = tokenizer.decode(
            result['generated_ids'][0],
            skip_special_tokens=True
        )
        
        decision_info = result['decision_info']
        decision_prob = decision_info['decision_probs'][0].item()
        confidence = decision_info['confidence'][0].item()
        
        logger.info(f"Generated: {generated_text[len(prompt):]}")
        logger.info(f"Used thinking: {result['thinking_used'][0].item()}")
        logger.info(f"Decision probability: {decision_prob:.3f}")
        logger.info(f"Confidence: {confidence:.3f}")
        
        # Evaluate decision correctness
        should_use_reasoning = expected_complexity in ['medium', 'hard']
        used_reasoning = result['thinking_used'][0].item()
        decision_correct = (should_use_reasoning and used_reasoning) or (not should_use_reasoning and not used_reasoning)
        
        logger.info(f"Decision correctness: {'CORRECT' if decision_correct else 'INCORRECT'}")


def test_reasoning_quality(model, tokenizer, test_cases, logger):
    """Test reasoning quality with specific examples."""
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    logger.info("\n" + "="*80)
    logger.info("TESTING REASONING QUALITY")
    logger.info("="*80)
    
    for case in test_cases:
        problem = case['problem']
        expected_steps = case['expected_steps']
        
        logger.info(f"\nProblem: {problem}")
        logger.info(f"Expected reasoning steps: {len(expected_steps)}")
        
        # Tokenize input
        input_ids = tokenizer.encode(problem, return_tensors='pt').to(device)
        
        # Generate with reasoning
        with torch.no_grad():
            result = model.generate(
                input_ids=input_ids,
                max_new_tokens=150,
                temperature=0.5,  # Lower temperature for more focused reasoning
                top_p=0.8,
                force_thinking=True,
                return_reasoning=True,
                tokenizer=tokenizer
            )
        
        # Analyze reasoning quality
        if 'reasoning_info' in result and result['reasoning_info']:
            reasoning_info = result['reasoning_info']
            
            if reasoning_info.get('used_reasoning', False):
                reasoning_steps = reasoning_info.get('reasoning_steps', [])
                quality_scores = reasoning_info.get('quality_scores', [])
                
                logger.info(f"Generated {len(reasoning_steps)} reasoning steps")
                
                if quality_scores:
                    avg_quality = sum(q.item() for q in quality_scores) / len(quality_scores)
                    logger.info(f"Average quality score: {avg_quality:.3f}")
                
                # Show expected vs actual
                logger.info("Expected reasoning steps:")
                for i, step in enumerate(expected_steps):
                    logger.info(f"  {i+1}. {step}")
                
                logger.info("Generated reasoning tokens:")
                for j, step_logits in enumerate(reasoning_steps):
                    step_probs = torch.softmax(step_logits[0, 0] / 0.5, dim=-1)
                    top_token = torch.argmax(step_probs)
                    top_token_text = tokenizer.decode([top_token])
                    logger.info(f"  {j+1}. Top token: '{top_token_text}' (prob: {step_probs[top_token]:.3f})")
        
        # Show final answer
        generated_text = tokenizer.decode(result['generated_ids'][0], skip_special_tokens=True)
        answer = generated_text[len(problem):].strip()
        logger.info(f"Final answer: {answer}")


def main():
    logger = setup_logging()
    logger.info("Starting supervised reasoning model demo...")

    # Create configuration
    config = create_demo_config()

    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token

    # Test prompts with varying complexity
    test_prompts = [
        {
            'prompt': "Hello, how are you?",
            'complexity': 'easy',
            'category': 'greeting'
        },
        {
            'prompt': "What is 2 + 2?",
            'complexity': 'easy',
            'category': 'math'
        },
        {
            'prompt': "The capital of France is",
            'complexity': 'easy',
            'category': 'facts'
        },
        {
            'prompt': "Solve the equation 3x + 7 = 22",
            'complexity': 'medium',
            'category': 'math'
        },
        {
            'prompt': "If a car travels 45 mph for 2.5 hours, how far does it go?",
            'complexity': 'medium',
            'category': 'math'
        },
        {
            'prompt': "Explain the process of photosynthesis",
            'complexity': 'hard',
            'category': 'science'
        },
        {
            'prompt': "Solve the quadratic equation x^2 + 5x + 6 = 0",
            'complexity': 'hard',
            'category': 'math'
        }
    ]

    # Reasoning quality test cases
    reasoning_test_cases = [
        {
            'problem': "Solve 2x + 5 = 13",
            'expected_steps': [
                "I need to solve for x in the equation 2x + 5 = 13",
                "First, I'll subtract 5 from both sides: 2x = 8",
                "Then I'll divide both sides by 2: x = 4"
            ]
        },
        {
            'problem': "What is 15% of 80?",
            'expected_steps': [
                "I need to calculate 15% of 80",
                "15% = 15/100 = 0.15",
                "0.15 × 80 = 12"
            ]
        }
    ]

    # Test different model versions
    model_checkpoints = [
        ('checkpoints/final_supervised_reasoning_model.pt', 'Final Supervised Model'),
        ('checkpoints/supervised_reasoning_model.pt', 'Phase 1 Supervised Model'),
        ('checkpoints/final_curriculum_model.pt', 'Curriculum Learning Model')
    ]

    for checkpoint_path, model_name in model_checkpoints:
        if not os.path.exists(checkpoint_path):
            logger.warning(f"Checkpoint not found: {checkpoint_path}")
            continue
            
        logger.info(f"\n{'='*100}")
        logger.info(f"TESTING {model_name.upper()}")
        logger.info(f"{'='*100}")

        # Load model
        model = load_supervised_model(checkpoint_path, config, logger)
        
        # Show model info
        model_info = model.get_model_info()
        logger.info(f"Model parameters: {model_info['total_params']:,}")
        
        # Test reasoning capabilities
        test_supervised_reasoning(model, tokenizer, test_prompts, logger)
        
        # Test reasoning quality
        test_reasoning_quality(model, tokenizer, reasoning_test_cases, logger)

    logger.info("\n" + "="*100)
    logger.info("SUPERVISED REASONING DEMO COMPLETED")
    logger.info("="*100)
    logger.info("\nKey improvements achieved:")
    logger.info("1. Supervised learning with step-by-step reasoning targets")
    logger.info("2. Curriculum learning (easy -> medium -> hard)")
    logger.info("3. Enhanced stopping criteria based on quality and coherence")
    logger.info("4. Better decision mechanism with complexity-based targets")
    logger.info("5. Progressive loss weighting for stable training")


if __name__ == '__main__':
    main()
