"""
Projection Layer - Projects ThinkerModule hidden states to main LLM hidden space.
Handles dimensional alignment and feature transformation between components.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any, Tuple
import math


class AdaptiveProjection(nn.Module):
    """
    Adaptive projection that learns to map ThinkerModule outputs to LLM space.
    Uses attention-based weighting to focus on relevant reasoning aspects.
    """

    def __init__(self, input_size: int, output_size: int, num_heads: int = 8):
        super().__init__()
        self.input_size = input_size
        self.output_size = output_size
        self.num_heads = num_heads

        # Cross-attention for adaptive projection
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=output_size,
            num_heads=num_heads,
            dropout=0.1,
            batch_first=True
        )

        # Query projection for LLM hidden states
        self.query_proj = nn.Linear(output_size, output_size)

        # Key-Value projection for ThinkerModule states
        self.kv_proj = nn.Linear(input_size, output_size * 2)

    def forward(self, thinker_states: torch.Tensor,
                llm_states: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Project ThinkerModule states using LLM states as queries.

        Args:
            thinker_states: ThinkerModule hidden states [batch_size, seq_len, input_size]
            llm_states: LLM hidden states [batch_size, seq_len, output_size]
            attention_mask: Attention mask [batch_size, seq_len]

        Returns:
            projected_states: Projected states [batch_size, seq_len, output_size]
        """
        # Project LLM states to queries
        queries = self.query_proj(llm_states)

        # Project ThinkerModule states to keys and values
        kv = self.kv_proj(thinker_states)
        keys, values = kv.chunk(2, dim=-1)

        # Apply cross-attention
        key_padding_mask = None
        if attention_mask is not None:
            # Convert attention mask to key padding mask (True for padding positions)
            key_padding_mask = (attention_mask == 0)

        projected_states, _ = self.cross_attention(
            query=queries,
            key=keys,
            value=values,
            key_padding_mask=key_padding_mask
        )

        return projected_states


class GatedProjection(nn.Module):
    """
    Gated projection that controls how much ThinkerModule information to use.
    """

    def __init__(self, input_size: int, output_size: int):
        super().__init__()
        self.input_size = input_size
        self.output_size = output_size

        # Main projection
        self.projection = nn.Linear(input_size, output_size)

        # Gate network
        self.gate_network = nn.Sequential(
            nn.Linear(input_size + output_size, output_size),
            nn.GELU(),
            nn.Linear(output_size, output_size),
            nn.Sigmoid()
        )

    def forward(self, thinker_states: torch.Tensor,
                llm_states: torch.Tensor) -> torch.Tensor:
        """
        Apply gated projection.

        Args:
            thinker_states: ThinkerModule hidden states [batch_size, seq_len, input_size]
            llm_states: LLM hidden states [batch_size, seq_len, output_size]

        Returns:
            gated_states: Gated projected states [batch_size, seq_len, output_size]
        """
        # Project ThinkerModule states
        projected = self.projection(thinker_states)

        # Compute gate values
        combined = torch.cat([thinker_states, llm_states], dim=-1)
        gate_values = self.gate_network(combined)

        # Apply gating
        gated_states = gate_values * projected + (1 - gate_values) * llm_states

        return gated_states


class ResidualProjection(nn.Module):
    """
    Residual projection with layer normalization and dropout.
    """

    def __init__(self, input_size: int, output_size: int,
                 num_layers: int = 2, dropout: float = 0.1):
        super().__init__()
        self.input_size = input_size
        self.output_size = output_size
        self.num_layers = num_layers

        # Build projection layers
        layers = []
        current_size = input_size

        for i in range(num_layers - 1):
            hidden_size = (input_size + output_size) // 2
            layers.extend([
                nn.Linear(current_size, hidden_size),
                nn.LayerNorm(hidden_size),
                nn.GELU(),
                nn.Dropout(dropout)
            ])
            current_size = hidden_size

        # Final projection
        layers.append(nn.Linear(current_size, output_size))

        self.projection_layers = nn.Sequential(*layers)

        # Residual connection (if dimensions match)
        self.use_residual = (input_size == output_size)
        if not self.use_residual and input_size != output_size:
            self.residual_proj = nn.Linear(input_size, output_size)

        self.layer_norm = nn.LayerNorm(output_size)

    def forward(self, thinker_states: torch.Tensor) -> torch.Tensor:
        """
        Apply residual projection.

        Args:
            thinker_states: ThinkerModule hidden states [batch_size, seq_len, input_size]

        Returns:
            projected_states: Projected states [batch_size, seq_len, output_size]
        """
        projected = self.projection_layers(thinker_states)

        # Add residual connection
        if self.use_residual:
            projected = projected + thinker_states
        elif hasattr(self, 'residual_proj'):
            projected = projected + self.residual_proj(thinker_states)

        return self.layer_norm(projected)


class ProjectionLayer(nn.Module):
    """
    Main projection layer that transforms ThinkerModule outputs for LLM integration.

    This component:
    1. Handles dimensional alignment between ThinkerModule and LLM
    2. Provides multiple projection strategies (adaptive, gated, residual)
    3. Maintains information flow while preventing interference
    4. Supports different integration modes
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config

        self.input_size = config['input_size']  # ThinkerModule hidden size
        self.output_size = config['output_size']  # LLM hidden size
        self.num_layers = config['num_layers']
        self.dropout = config['dropout']

        # Projection strategy
        self.projection_type = config.get('projection_type', 'adaptive')

        if self.projection_type == 'adaptive':
            self.projection = AdaptiveProjection(
                input_size=self.input_size,
                output_size=self.output_size,
                num_heads=config.get('num_heads', 8)
            )
        elif self.projection_type == 'gated':
            self.projection = GatedProjection(
                input_size=self.input_size,
                output_size=self.output_size
            )
        elif self.projection_type == 'residual':
            self.projection = ResidualProjection(
                input_size=self.input_size,
                output_size=self.output_size,
                num_layers=self.num_layers,
                dropout=self.dropout
            )
        else:
            # Simple linear projection
            self.projection = nn.Linear(self.input_size, self.output_size)

        # Integration mode controls how projected states are combined with LLM states
        self.integration_mode = config.get('integration_mode', 'addition')

        if self.integration_mode == 'concatenation':
            # If concatenating, we need to project back to original size
            self.integration_proj = nn.Linear(self.output_size * 2, self.output_size)
        elif self.integration_mode == 'weighted_sum':
            # Learnable weights for combining states
            self.integration_weights = nn.Parameter(torch.tensor([0.5, 0.5]))

        # Output layer norm and dropout
        self.output_norm = nn.LayerNorm(self.output_size)
        self.output_dropout = nn.Dropout(self.dropout)

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)

    def forward(self, thinker_states: torch.Tensor,
                llm_states: Optional[torch.Tensor] = None,
                attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Project ThinkerModule states for LLM integration.

        Args:
            thinker_states: ThinkerModule hidden states [batch_size, seq_len, input_size]
            llm_states: LLM hidden states [batch_size, seq_len, output_size] (optional)
            attention_mask: Attention mask [batch_size, seq_len]

        Returns:
            enhanced_states: Enhanced hidden states for LLM [batch_size, seq_len, output_size]
        """
        # Apply projection based on type
        if self.projection_type == 'adaptive' and llm_states is not None:
            projected_states = self.projection(thinker_states, llm_states, attention_mask)
        elif self.projection_type == 'gated' and llm_states is not None:
            projected_states = self.projection(thinker_states, llm_states)
        else:
            projected_states = self.projection(thinker_states)

        # Integrate with LLM states if provided
        if llm_states is not None:
            enhanced_states = self._integrate_states(projected_states, llm_states)
        else:
            enhanced_states = projected_states

        # Apply output normalization and dropout
        enhanced_states = self.output_norm(enhanced_states)
        enhanced_states = self.output_dropout(enhanced_states)

        return enhanced_states

    def _integrate_states(self, projected_states: torch.Tensor,
                         llm_states: torch.Tensor) -> torch.Tensor:
        """
        Integrate projected ThinkerModule states with LLM states.

        Args:
            projected_states: Projected ThinkerModule states
            llm_states: Original LLM states

        Returns:
            integrated_states: Combined states
        """
        if self.integration_mode == 'addition':
            return projected_states + llm_states
        elif self.integration_mode == 'concatenation':
            concatenated = torch.cat([projected_states, llm_states], dim=-1)
            return self.integration_proj(concatenated)
        elif self.integration_mode == 'weighted_sum':
            weights = F.softmax(self.integration_weights, dim=0)
            return weights[0] * projected_states + weights[1] * llm_states
        else:
            # Default to addition
            return projected_states + llm_states

    def get_projection_info(self) -> Dict[str, Any]:
        """Get information about the projection configuration."""
        return {
            'input_size': self.input_size,
            'output_size': self.output_size,
            'projection_type': self.projection_type,
            'integration_mode': self.integration_mode,
            'num_layers': self.num_layers,
            'dropout': self.dropout
        }
