"""
Retrain the model with improved loss functions and configurations.
This script addresses the issues found in the inference outputs.
"""

import os
import sys
import yaml
import torch
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from transformers import AutoTokenizer
from src.models.integrated_model import IntegratedLL<PERSON><PERSON><PERSON><PERSON><PERSON>ker
from src.training.trainer import Trainer
from src.training.data_loader import DataLoader


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('retrain.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Convert string scientific notation to float
    def convert_scientific_notation(obj):
        if isinstance(obj, dict):
            return {k: convert_scientific_notation(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_scientific_notation(item) for item in obj]
        elif isinstance(obj, str):
            try:
                if 'e-' in obj.lower() or 'e+' in obj.lower():
                    return float(obj)
                return obj
            except ValueError:
                return obj
        else:
            return obj

    return convert_scientific_notation(config)


def validate_training_data(data_path: str, logger):
    """Validate that training data exists and is properly formatted."""
    if not os.path.exists(data_path):
        logger.error(f"Training data not found: {data_path}")
        return False
    
    # Check first few lines to ensure proper format
    try:
        import json
        with open(data_path, 'r') as f:
            for i, line in enumerate(f):
                if i >= 3:  # Check first 3 lines
                    break
                data = json.loads(line.strip())
                if 'text' not in data:
                    logger.error(f"Invalid data format in line {i+1}: missing 'text' field")
                    return False
        logger.info(f"Training data validation passed: {data_path}")
        return True
    except Exception as e:
        logger.error(f"Error validating training data: {e}")
        return False


def main():
    logger = setup_logging()
    logger.info("Starting improved model retraining...")

    # Load configuration
    config_path = 'config/model_config.yaml'
    logger.info(f"Loading configuration from {config_path}")
    config = load_config(config_path)

    # Validate training data
    train_file = config['data']['train_file']
    if not validate_training_data(train_file, logger):
        logger.error("Training data validation failed. Exiting.")
        return

    # Initialize tokenizer
    logger.info("Initializing tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token

    # Initialize model
    logger.info("Initializing model...")
    model = IntegratedLLMWithThinker(config['model'])
    
    # Show model info
    model_info = model.get_model_info()
    logger.info(f"Model initialized with {model_info['total_params']:,} parameters")
    logger.info(f"  LLM: {model_info['component_info']['llm_params']:,}")
    logger.info(f"  ThinkerModule: {model_info['component_info']['thinker_params']:,}")
    logger.info(f"  Decision Mechanism: {model_info['component_info']['decision_params']:,}")

    # Create data loaders
    logger.info("Creating data loaders...")
    data_loader = DataLoader(config, tokenizer)
    train_loader = data_loader.create_train_loader()
    eval_loader = data_loader.create_eval_loader()

    logger.info(f"Training batches: {len(train_loader)}")
    if eval_loader:
        logger.info(f"Evaluation batches: {len(eval_loader)}")

    # Initialize trainer with improved configuration
    logger.info("Initializing trainer...")
    trainer = Trainer(model, config)

    # Log training configuration
    logger.info("Training Configuration:")
    logger.info(f"  Batch size: {config['training']['batch_size']}")
    logger.info(f"  Learning rate: {config['training']['learning_rate']}")
    logger.info(f"  Number of epochs: {config['training']['num_epochs']}")
    logger.info(f"  Loss weights:")
    logger.info(f"    LLM: {config['training']['llm_loss_weight']}")
    logger.info(f"    Thinker: {config['training']['thinker_loss_weight']}")
    logger.info(f"    Decision: {config['training']['decision_loss_weight']}")
    logger.info(f"    Projection: {config['training']['projection_loss_weight']}")

    # Start training
    logger.info("Starting training...")
    try:
        history = trainer.train(train_loader, eval_loader)
        logger.info("Training completed successfully!")
        
        # Log final statistics
        final_stats = trainer.get_training_info()
        logger.info("Final Training Statistics:")
        logger.info(f"  Final epoch: {final_stats['current_epoch']}")
        logger.info(f"  Total steps: {final_stats['global_step']}")
        logger.info(f"  Best loss: {final_stats['best_loss']:.4f}")
        
        # Save final model
        trainer.save_checkpoint('final_improved_model')
        logger.info("Final model saved as 'final_improved_model.pt'")
        
    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return

    logger.info("Retraining completed!")


if __name__ == '__main__':
    main()
