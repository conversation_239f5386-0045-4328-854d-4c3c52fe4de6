"""
Example training script for Integrated LLM with ThinkerModule.
Demonstrates how to set up and train the model with different configurations.
"""

import os
import sys
import yaml
import logging
import argparse
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

import torch
from transformers import AutoTokenizer
import wandb

from src.models.integrated_model import <PERSON><PERSON>MWithThinker
from src.training.trainer import Trainer
from src.training.data_loader import DataLoader


def setup_logging(log_level: str = 'INFO'):
    """Setup logging configuration."""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('training.log'),
            logging.StreamHandler()
        ]
    )


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Convert string scientific notation to float
    def convert_scientific_notation(obj):
        if isinstance(obj, dict):
            return {k: convert_scientific_notation(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_scientific_notation(item) for item in obj]
        elif isinstance(obj, str):
            try:
                # Try to convert scientific notation strings to float
                if 'e-' in obj.lower() or 'e+' in obj.lower():
                    return float(obj)
                return obj
            except ValueError:
                return obj
        else:
            return obj

    return convert_scientific_notation(config)


def create_sample_data():
    """Create sample training data for demonstration."""
    import json

    # Create data directory
    os.makedirs('data', exist_ok=True)

    # Sample training data
    train_data = [
        {
            "text": "The capital of France is Paris. It is known for the Eiffel Tower.",
            "needs_thinking": False
        },
        {
            "text": "To solve the equation 2x + 5 = 13, we need to isolate x. First, subtract 5 from both sides: 2x = 8. Then divide by 2: x = 4.",
            "needs_thinking": True,
            "reasoning": [
                "Start with equation 2x + 5 = 13",
                "Subtract 5 from both sides",
                "Get 2x = 8",
                "Divide both sides by 2",
                "Final answer: x = 4"
            ]
        },
        {
            "text": "The weather is nice today.",
            "needs_thinking": False
        },
        {
            "text": "If a train travels at 60 mph for 2.5 hours, how far does it go? Distance = speed × time = 60 × 2.5 = 150 miles.",
            "needs_thinking": True,
            "reasoning": [
                "Identify the formula: Distance = speed × time",
                "Substitute values: speed = 60 mph, time = 2.5 hours",
                "Calculate: 60 × 2.5 = 150",
                "Answer: 150 miles"
            ]
        }
    ]

    # Save training data
    with open('data/train.jsonl', 'w') as f:
        for item in train_data:
            f.write(json.dumps(item) + '\n')

    # Create smaller eval set
    eval_data = train_data[:2]
    with open('data/eval.jsonl', 'w') as f:
        for item in eval_data:
            f.write(json.dumps(item) + '\n')

    print("Sample data created in 'data/' directory")


def main():
    parser = argparse.ArgumentParser(description='Train Integrated LLM with ThinkerModule')
    parser.add_argument('--config', type=str, default='config/model_config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--create-data', action='store_true',
                       help='Create sample training data')
    parser.add_argument('--use-wandb', action='store_true',
                       help='Use Weights & Biases for logging')
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Logging level')

    args = parser.parse_args()

    # Setup logging
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)

    # Create sample data if requested
    if args.create_data:
        create_sample_data()
        return

    # Load configuration
    logger.info(f"Loading configuration from {args.config}")
    config = load_config(args.config)

    # Update config with command line arguments
    config['training']['use_wandb'] = args.use_wandb

    # Initialize wandb if requested
    if args.use_wandb:
        wandb.init(
            project="llm-thinker-module",
            config=config,
            name=f"training_{config['training'].get('training_mode', 'joint')}"
        )

    # Initialize tokenizer
    logger.info("Initializing tokenizer")
    tokenizer = AutoTokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token

    # Create data loaders
    logger.info("Creating data loaders")
    data_loader = DataLoader(config, tokenizer)

    try:
        train_loader = data_loader.create_train_loader()
        eval_loader = data_loader.create_eval_loader()

        logger.info(f"Training samples: {len(train_loader.dataset)}")
        if eval_loader:
            logger.info(f"Evaluation samples: {len(eval_loader.dataset)}")

    except FileNotFoundError as e:
        logger.error(f"Data file not found: {e}")
        logger.info("Run with --create-data to generate sample data")
        return

    # Initialize model
    logger.info("Initializing model")
    model = IntegratedLLMWithThinker(config['model'])

    # Log model information
    model_info = model.get_model_info()
    logger.info(f"Total parameters: {model_info['total_params']:,}")
    logger.info(f"LLM parameters: {model_info['component_info']['llm_params']:,}")
    logger.info(f"ThinkerModule parameters: {model_info['component_info']['thinker_params']:,}")

    # Initialize trainer
    logger.info("Initializing trainer")
    trainer = Trainer(model, config)

    # Start training
    logger.info("Starting training")
    try:
        history = trainer.train(train_loader, eval_loader)

        logger.info("Training completed successfully")
        logger.info(f"Final training loss: {history['train_loss'][-1]:.4f}")

        if history['eval_loss']:
            logger.info(f"Final evaluation loss: {history['eval_loss'][-1]:.4f}")

        # Save final model
        trainer.save_checkpoint('final_model')

        # Log final statistics
        final_stats = model.get_current_stats()
        logger.info(f"ThinkerModule usage rate: {final_stats['thinker_usage_rate']:.2%}")
        logger.info(f"Direct LLM usage rate: {final_stats['direct_llm_rate']:.2%}")

    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        trainer.save_checkpoint('interrupted_model')

    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        raise

    finally:
        if args.use_wandb:
            wandb.finish()


if __name__ == '__main__':
    main()
