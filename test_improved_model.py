"""
Test the improved model and compare with previous version.
This script helps validate that the improvements are working.
"""

import os
import sys
import yaml
import torch
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from transformers import AutoTokenizer
from src.models.integrated_model import IntegratedLLMWithThinker


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    def convert_scientific_notation(obj):
        if isinstance(obj, dict):
            return {k: convert_scientific_notation(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_scientific_notation(item) for item in obj]
        elif isinstance(obj, str):
            try:
                if 'e-' in obj.lower() or 'e+' in obj.lower():
                    return float(obj)
                return obj
            except ValueError:
                return obj
        else:
            return obj

    return convert_scientific_notation(config)


def load_model(checkpoint_path: str, config: dict, logger) -> IntegratedLLMWithThinker:
    """Load model from checkpoint."""
    model_config = config['model'] if 'model' in config else config
    model = IntegratedLLMWithThinker(model_config)

    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"Model loaded from {checkpoint_path}")
        logger.info(f"Training epoch: {checkpoint.get('epoch', 'unknown')}")
        logger.info(f"Training step: {checkpoint.get('global_step', 'unknown')}")
        logger.info(f"Best loss: {checkpoint.get('best_loss', 'unknown')}")
    else:
        logger.warning(f"Checkpoint not found: {checkpoint_path}")
        logger.warning("Using randomly initialized model")

    return model


def test_reasoning_quality(model, tokenizer, prompts, logger):
    """Test the quality of reasoning generation."""
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    logger.info("Testing reasoning quality...")
    
    reasoning_quality_scores = []
    
    for i, prompt in enumerate(prompts):
        logger.info(f"\nTest {i+1}: {prompt}")
        
        # Tokenize input
        input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)
        
        # Generate with reasoning
        with torch.no_grad():
            result = model.generate(
                input_ids=input_ids,
                max_new_tokens=50,
                temperature=0.7,
                top_p=0.9,
                force_thinking=True,
                return_reasoning=True
            )
        
        # Decode generated text
        generated_text = tokenizer.decode(
            result['generated_ids'][0],
            skip_special_tokens=True
        )
        
        logger.info(f"Generated: {generated_text[len(prompt):]}")
        logger.info(f"Used thinking: {result['thinking_used'][0].item()}")
        
        # Analyze reasoning quality
        if 'reasoning_info' in result and 'reasoning_steps' in result['reasoning_info']:
            reasoning_info = result['reasoning_info']
            reasoning_texts = model.thinker.generate_reasoning_text(
                reasoning_info['reasoning_steps'],
                tokenizer,
                max_tokens_per_step=10,
                stop_probabilities=reasoning_info.get('stop_probabilities')
            )
            
            logger.info(f"Reasoning steps ({len(reasoning_texts)}):")
            for j, step_text in enumerate(reasoning_texts):
                logger.info(f"  Step {j+1}: {step_text}")
            
            # Simple quality metric: check if reasoning contains meaningful words
            meaningful_words = ['calculate', 'solve', 'answer', 'step', 'first', 'then', 'next', 'because', 'therefore']
            quality_score = 0
            for step_text in reasoning_texts:
                step_lower = step_text.lower()
                for word in meaningful_words:
                    if word in step_lower:
                        quality_score += 1
                        break
            
            quality_score = quality_score / len(reasoning_texts) if reasoning_texts else 0
            reasoning_quality_scores.append(quality_score)
            logger.info(f"Reasoning quality score: {quality_score:.2f}")
        else:
            logger.info("No reasoning steps generated")
            reasoning_quality_scores.append(0.0)
    
    avg_quality = sum(reasoning_quality_scores) / len(reasoning_quality_scores) if reasoning_quality_scores else 0
    logger.info(f"\nAverage reasoning quality score: {avg_quality:.2f}")
    return avg_quality


def test_decision_mechanism(model, tokenizer, prompts, logger):
    """Test the decision mechanism behavior."""
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    logger.info("Testing decision mechanism...")
    
    decision_stats = {'thinking_used': 0, 'direct_used': 0}
    
    for i, prompt in enumerate(prompts):
        # Tokenize input
        input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)
        
        # Test automatic decision
        with torch.no_grad():
            result = model.generate(
                input_ids=input_ids,
                max_new_tokens=20,
                temperature=0.7,
                force_thinking=None,  # Automatic decision
                return_reasoning=False
            )
        
        thinking_used = result['thinking_used'][0].item()
        decision_info = result['decision_info']
        
        if thinking_used:
            decision_stats['thinking_used'] += 1
        else:
            decision_stats['direct_used'] += 1
        
        decision_prob = decision_info.get('decision_probs', [torch.tensor(0.0)])[0].item()
        confidence = decision_info.get('confidence', [torch.tensor(0.0)])[0].item()
        
        logger.info(f"Prompt: {prompt[:50]}...")
        logger.info(f"  Decision: {'Thinking' if thinking_used else 'Direct'}")
        logger.info(f"  Probability: {decision_prob:.3f}")
        logger.info(f"  Confidence: {confidence:.3f}")
    
    total_prompts = len(prompts)
    thinking_ratio = decision_stats['thinking_used'] / total_prompts
    logger.info(f"\nDecision Statistics:")
    logger.info(f"  Thinking used: {decision_stats['thinking_used']}/{total_prompts} ({thinking_ratio:.1%})")
    logger.info(f"  Direct used: {decision_stats['direct_used']}/{total_prompts} ({1-thinking_ratio:.1%})")
    
    return decision_stats


def main():
    logger = setup_logging()
    logger.info("Testing improved model...")

    # Load configuration
    config_path = 'config/model_config.yaml'
    config = load_config(config_path)

    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token

    # Test prompts - mix of simple and complex
    test_prompts = [
        "The capital of France is",
        "To solve 2x + 5 = 13, we need to",
        "What is the weather like today?",
        "If a car travels 60 miles per hour for 3 hours, how far does it travel?",
        "Explain the concept of photosynthesis",
        "Hello, how are you?",
        "Calculate the area of a circle with radius 5",
        "What is 2 + 2?",
        "Describe the process of solving a quadratic equation",
        "The sky is blue because"
    ]

    # Test different model versions
    model_checkpoints = [
        ('checkpoints/best_model.pt', 'Original Model'),
        ('checkpoints/final_improved_model.pt', 'Improved Model')
    ]

    results = {}

    for checkpoint_path, model_name in model_checkpoints:
        if not os.path.exists(checkpoint_path):
            logger.warning(f"Checkpoint not found: {checkpoint_path}")
            continue
            
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing {model_name}")
        logger.info(f"{'='*60}")

        # Load model
        model = load_model(checkpoint_path, config, logger)
        
        # Test reasoning quality
        reasoning_quality = test_reasoning_quality(model, tokenizer, test_prompts[:5], logger)
        
        # Test decision mechanism
        decision_stats = test_decision_mechanism(model, tokenizer, test_prompts, logger)
        
        results[model_name] = {
            'reasoning_quality': reasoning_quality,
            'decision_stats': decision_stats
        }

    # Compare results
    logger.info(f"\n{'='*60}")
    logger.info("COMPARISON RESULTS")
    logger.info(f"{'='*60}")

    for model_name, result in results.items():
        logger.info(f"\n{model_name}:")
        logger.info(f"  Reasoning Quality: {result['reasoning_quality']:.2f}")
        logger.info(f"  Thinking Usage: {result['decision_stats']['thinking_used']}/{len(test_prompts)}")

    logger.info("\nTesting completed!")


if __name__ == '__main__':
    main()
