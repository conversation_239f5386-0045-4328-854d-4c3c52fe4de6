"""
Inference demonstration for Integrated LLM with ThinkerModule.
Shows how to use the trained model for text generation with reasoning.
"""

import os
import sys
import yaml
import torch
import argparse
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from transformers import AutoTokenizer
from src.models.integrated_model import IntegratedLLMWithThinker


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Convert string scientific notation to float
    def convert_scientific_notation(obj):
        if isinstance(obj, dict):
            return {k: convert_scientific_notation(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_scientific_notation(item) for item in obj]
        elif isinstance(obj, str):
            try:
                # Try to convert scientific notation strings to float
                if 'e-' in obj.lower() or 'e+' in obj.lower():
                    return float(obj)
                return obj
            except ValueError:
                return obj
        else:
            return obj

    return convert_scientific_notation(config)


def load_model(checkpoint_path: str, config: dict) -> IntegratedLLMWithThinker:
    """Load trained model from checkpoint."""
    # Handle nested config structure
    model_config = config['model'] if 'model' in config else config
    model = IntegratedLLMWithThinker(model_config)

    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Model loaded from {checkpoint_path}")
        print(f"Training epoch: {checkpoint.get('epoch', 'unknown')}")
        print(f"Training step: {checkpoint.get('global_step', 'unknown')}")
    else:
        print(f"Checkpoint not found: {checkpoint_path}")
        print("Using randomly initialized model")

    return model


def demonstrate_inference(model: IntegratedLLMWithThinker,
                         tokenizer: AutoTokenizer,
                         prompts: list,
                         config: dict):
    """Demonstrate inference with different prompts."""
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    inference_config = config['inference']

    print("\n" + "="*80)
    print("INFERENCE DEMONSTRATION")
    print("="*80)

    for i, prompt in enumerate(prompts, 1):
        print(f"\n--- Example {i} ---")
        print(f"Prompt: {prompt}")

        # Tokenize input
        input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)

        # Generate with reasoning
        print("\n🧠 Generating with reasoning enabled:")
        result_with_thinking = model.generate(
            input_ids=input_ids,
            max_new_tokens=inference_config['max_new_tokens'],
            temperature=inference_config['temperature'],
            top_p=inference_config['top_p'],
            force_thinking=True,
            return_reasoning=True
        )

        # Decode generated text
        generated_text = tokenizer.decode(
            result_with_thinking['generated_ids'][0],
            skip_special_tokens=True
        )

        print(f"Generated: {generated_text[len(prompt):]}")
        print(f"Used thinking: {result_with_thinking['thinking_used'][0].item()}")

        # Show reasoning steps if available
        if 'reasoning_info' in result_with_thinking:
            reasoning_info = result_with_thinking['reasoning_info']
            if 'reasoning_steps' in reasoning_info:
                print(f"\n💭 Reasoning steps ({reasoning_info.get('num_reasoning_steps', len(reasoning_info['reasoning_steps']))} steps generated):")
                reasoning_texts = model.thinker.generate_reasoning_text(
                    reasoning_info['reasoning_steps'],
                    tokenizer,
                    max_tokens_per_step=10,
                    stop_probabilities=reasoning_info.get('stop_probabilities')
                )
                for step_text in reasoning_texts:
                    print(f"  {step_text}")

        # Generate without forced thinking (automatic decision)
        print("\n🤖 Generating with automatic decision:")
        result_auto = model.generate(
            input_ids=input_ids,
            max_new_tokens=inference_config['max_new_tokens'],
            temperature=inference_config['temperature'],
            top_p=inference_config['top_p'],
            force_thinking=None,
            return_reasoning=False
        )

        generated_text_auto = tokenizer.decode(
            result_auto['generated_ids'][0],
            skip_special_tokens=True
        )

        print(f"Generated: {generated_text_auto[len(prompt):]}")
        print(f"Used thinking: {result_auto['thinking_used'][0].item()}")

        # Show decision information
        decision_info = result_auto['decision_info']
        if 'decision_probs' in decision_info:
            decision_prob = decision_info['decision_probs'][0].item()
            confidence = decision_info.get('confidence', [torch.tensor(0.0)])[0].item()
            print(f"Decision probability: {decision_prob:.3f}")
            print(f"Confidence: {confidence:.3f}")

        print("-" * 60)


def interactive_mode(model: IntegratedLLMWithThinker,
                    tokenizer: AutoTokenizer,
                    config: dict):
    """Interactive mode for testing the model."""
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    inference_config = config['inference']

    print("\n" + "="*80)
    print("INTERACTIVE MODE")
    print("="*80)
    print("Enter prompts to test the model. Type 'quit' to exit.")
    print("Commands:")
    print("  'stats' - Show model statistics")
    print("  'threshold <value>' - Set decision threshold")
    print("  'quit' - Exit interactive mode")
    print("-" * 80)

    while True:
        try:
            prompt = input("\nPrompt: ").strip()

            if prompt.lower() == 'quit':
                break
            elif prompt.lower() == 'stats':
                stats = model.get_current_stats()
                print("\nModel Statistics:")
                for key, value in stats.items():
                    if isinstance(value, float):
                        print(f"  {key}: {value:.4f}")
                    else:
                        print(f"  {key}: {value}")
                continue
            elif prompt.lower().startswith('threshold'):
                try:
                    parts = prompt.split()
                    if len(parts) == 2:
                        threshold = float(parts[1])
                        model.set_decision_threshold(threshold)
                        print(f"Decision threshold set to {threshold}")
                    else:
                        print("Usage: threshold <value>")
                except ValueError:
                    print("Invalid threshold value")
                continue
            elif not prompt:
                continue

            # Tokenize input
            input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)

            # Generate with automatic decision
            result = model.generate(
                input_ids=input_ids,
                max_new_tokens=inference_config['max_new_tokens'],
                temperature=inference_config['temperature'],
                top_p=inference_config['top_p'],
                force_thinking=None,
                return_reasoning=True
            )

            # Decode and display result
            generated_text = tokenizer.decode(
                result['generated_ids'][0],
                skip_special_tokens=True
            )

            print(f"\nGenerated: {generated_text[len(prompt):]}")
            print(f"Used thinking: {result['thinking_used'][0].item()}")

            # Show decision details
            decision_info = result['decision_info']
            if 'decision_probs' in decision_info:
                decision_prob = decision_info['decision_probs'][0].item()
                confidence = decision_info.get('confidence', [torch.tensor(0.0)])[0].item()
                print(f"Decision probability: {decision_prob:.3f}")
                print(f"Confidence: {confidence:.3f}")

            # Show reasoning if used
            if (result['thinking_used'][0].item() and
                'reasoning_info' in result and
                'reasoning_steps' in result['reasoning_info']):

                reasoning_info = result['reasoning_info']
                num_steps = reasoning_info.get('num_reasoning_steps', len(reasoning_info['reasoning_steps']))
                print(f"\nReasoning steps ({num_steps} steps generated):")
                reasoning_texts = model.thinker.generate_reasoning_text(
                    reasoning_info['reasoning_steps'],
                    tokenizer,
                    max_tokens_per_step=10,
                    stop_probabilities=reasoning_info.get('stop_probabilities')
                )
                for step_text in reasoning_texts:
                    print(f"  {step_text}")

        except KeyboardInterrupt:
            print("\nExiting interactive mode...")
            break
        except Exception as e:
            print(f"Error: {e}")


def main():
    parser = argparse.ArgumentParser(description='Inference demo for Integrated LLM with ThinkerModule')
    parser.add_argument('--config', type=str, default='config/model_config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--checkpoint', type=str, default='checkpoints/best_model.pt',
                       help='Path to model checkpoint')
    parser.add_argument('--interactive', action='store_true',
                       help='Run in interactive mode')

    args = parser.parse_args()

    # Load configuration
    print(f"Loading configuration from {args.config}")
    config = load_config(args.config)

    # Initialize tokenizer
    print("Initializing tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token

    # Load model
    print("Loading model...")
    model = load_model(args.checkpoint, config)

    # Show model info
    model_info = model.get_model_info()
    print(f"Total parameters: {model_info['total_params']:,}")

    if args.interactive:
        interactive_mode(model, tokenizer, config)
    else:
        # Demo prompts
        demo_prompts = [
            "The capital of France is",
            "To solve 2x + 5 = 13, we need to",
            "What is the weather like today?",
            "If a car travels 60 miles per hour for 3 hours, how far does it travel?",
            "Explain the concept of photosynthesis",
            "Hello, how are you?"
        ]

        demonstrate_inference(model, tokenizer, demo_prompts, config)


if __name__ == '__main__':
    main()
