2025-05-29 14:15:16,315 - INFO - Creating sample input file...
2025-05-29 14:15:16,395 - INFO - Added provider: openai (model: gpt-4o-mini)
2025-05-29 14:15:16,395 - INFO - Starting large dataset generation from sample_input.txt
2025-05-29 14:15:16,395 - INFO - Batch size: 25, Max examples: 100
2025-05-29 14:15:16,396 - INFO - Loaded 100 texts to process
2025-05-29 14:15:16,396 - INFO - Processing batch 1/4
2025-05-29 14:15:16,396 - INFO - Using provider: openai
2025-05-29 14:15:22,917 - INFO - Processing batch 2/4
2025-05-29 14:15:22,917 - INFO - Using provider: openai
2025-05-29 14:15:28,555 - INFO - Processing batch 3/4
2025-05-29 14:15:28,556 - INFO - Using provider: openai
2025-05-29 14:15:34,208 - INFO - Processing batch 4/4
2025-05-29 14:15:34,209 - INFO - Using provider: openai
2025-05-29 14:15:39,725 - INFO - ==================================================
2025-05-29 14:15:39,725 - INFO - FINAL STATISTICS
2025-05-29 14:15:39,725 - INFO - ==================================================
2025-05-29 14:15:39,725 - INFO - Total time: 23.33 seconds
2025-05-29 14:15:39,725 - INFO - Examples processed this session: 96
2025-05-29 14:15:39,725 - INFO - Duplicates skipped: 4
2025-05-29 14:15:39,725 - INFO - Errors encountered: 0
2025-05-29 14:15:39,725 - INFO - Total examples in database: 93
2025-05-29 14:15:39,725 - INFO - Average rate: 4.12 examples/second
2025-05-29 14:15:39,727 - INFO - Exported data to training_data_1748502939.jsonl
