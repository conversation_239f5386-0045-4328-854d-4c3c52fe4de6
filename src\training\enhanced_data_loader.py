"""
Enhanced data loader for reasoning training data with proper supervision.
"""

import json
import torch
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Optional, Any
import random


class ReasoningDataset(Dataset):
    """Dataset for reasoning training with step-by-step supervision."""
    
    def __init__(self, data_path: str, tokenizer, max_length: int = 512, max_reasoning_steps: int = 16):
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.max_reasoning_steps = max_reasoning_steps
        self.examples = []
        
        # Load data
        with open(data_path, 'r') as f:
            for line in f:
                data = json.loads(line.strip())
                self.examples.append(data)
        
        print(f"Loaded {len(self.examples)} examples from {data_path}")
    
    def __len__(self):
        return len(self.examples)
    
    def __getitem__(self, idx):
        example = self.examples[idx]
        
        # Extract components
        text = example['text']
        reasoning_steps = example.get('reasoning_steps', [])
        complexity = example.get('complexity', 'medium')
        category = example.get('category', 'general')
        
        # Tokenize main text
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        input_ids = encoding['input_ids'].squeeze(0)
        attention_mask = encoding['attention_mask'].squeeze(0)
        
        # Labels for language modeling (same as input_ids)
        labels = input_ids.clone()
        
        # Process reasoning steps
        reasoning_targets = []
        if reasoning_steps:
            for step in reasoning_steps[:self.max_reasoning_steps]:
                step_encoding = self.tokenizer(
                    step,
                    truncation=True,
                    padding='max_length',
                    max_length=50,  # Shorter length for reasoning steps
                    return_tensors='pt'
                )
                reasoning_targets.append(step_encoding['input_ids'].squeeze(0))
        
        # Determine if reasoning should be used
        use_reasoning = len(reasoning_steps) > 0
        
        # Complexity-based decision target
        complexity_to_decision = {
            'easy': 0.2,    # Low probability of using reasoning
            'medium': 0.7,  # High probability of using reasoning
            'hard': 0.9     # Very high probability of using reasoning
        }
        decision_target = complexity_to_decision.get(complexity, 0.5)
        
        return {
            'input_ids': input_ids,
            'attention_mask': attention_mask,
            'labels': labels,
            'reasoning_targets': reasoning_targets,
            'decision_target': torch.tensor(decision_target, dtype=torch.float),
            'use_reasoning': torch.tensor(use_reasoning, dtype=torch.bool),
            'complexity': complexity,
            'category': category,
            'num_reasoning_steps': len(reasoning_steps)
        }


def enhanced_collate_fn(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Enhanced collate function for reasoning training."""
    # Standard fields
    input_ids = torch.stack([item['input_ids'] for item in batch])
    attention_mask = torch.stack([item['attention_mask'] for item in batch])
    labels = torch.stack([item['labels'] for item in batch])
    
    # Decision targets
    decision_targets = torch.stack([item['decision_target'] for item in batch])
    use_reasoning = torch.stack([item['use_reasoning'] for item in batch])
    
    # Reasoning targets - handle variable lengths
    max_steps = max(len(item['reasoning_targets']) for item in batch)
    reasoning_targets = []
    reasoning_lengths = []
    
    for item in batch:
        item_targets = item['reasoning_targets']
        item_length = len(item_targets)
        reasoning_lengths.append(item_length)
        
        # Pad to max_steps
        if item_length < max_steps:
            # Create padding tensors
            pad_tensor = torch.zeros_like(item_targets[0]) if item_targets else torch.zeros(50, dtype=torch.long)
            padding = [pad_tensor] * (max_steps - item_length)
            item_targets = item_targets + padding
        
        reasoning_targets.append(item_targets[:max_steps])
    
    # Convert to tensor format
    if max_steps > 0:
        reasoning_targets = torch.stack([torch.stack(targets) for targets in reasoning_targets])
    else:
        reasoning_targets = torch.empty(len(batch), 0, 50, dtype=torch.long)
    
    reasoning_lengths = torch.tensor(reasoning_lengths, dtype=torch.long)
    
    # Additional metadata
    complexities = [item['complexity'] for item in batch]
    categories = [item['category'] for item in batch]
    num_reasoning_steps = torch.tensor([item['num_reasoning_steps'] for item in batch])
    
    return {
        'input_ids': input_ids,
        'attention_mask': attention_mask,
        'labels': labels,
        'reasoning_targets': reasoning_targets,
        'reasoning_lengths': reasoning_lengths,
        'decision_targets': decision_targets,
        'use_reasoning': use_reasoning,
        'complexities': complexities,
        'categories': categories,
        'num_reasoning_steps': num_reasoning_steps
    }


class EnhancedDataLoader:
    """Enhanced data loader for reasoning training."""
    
    def __init__(self, config: Dict[str, Any], tokenizer):
        self.config = config
        self.tokenizer = tokenizer
        self.max_length = config['data'].get('max_length', 512)
        self.max_reasoning_steps = config['model']['thinker'].get('max_reasoning_steps', 16)
    
    def create_train_loader(self) -> DataLoader:
        """Create training data loader."""
        train_file = self.config['data'].get('train_file', 'data/enhanced_train.jsonl')
        
        dataset = ReasoningDataset(
            train_file,
            self.tokenizer,
            self.max_length,
            self.max_reasoning_steps
        )
        
        return DataLoader(
            dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=True,
            collate_fn=enhanced_collate_fn,
            num_workers=0  # Set to 0 for Windows compatibility
        )
    
    def create_eval_loader(self) -> Optional[DataLoader]:
        """Create evaluation data loader."""
        eval_file = self.config['data'].get('eval_file', 'data/enhanced_eval.jsonl')
        
        try:
            dataset = ReasoningDataset(
                eval_file,
                self.tokenizer,
                self.max_length,
                self.max_reasoning_steps
            )
            
            return DataLoader(
                dataset,
                batch_size=self.config['training']['batch_size'],
                shuffle=False,
                collate_fn=enhanced_collate_fn,
                num_workers=0
            )
        except FileNotFoundError:
            print(f"Evaluation file {eval_file} not found. Skipping evaluation.")
            return None


class CurriculumDataLoader:
    """Data loader with curriculum learning support."""
    
    def __init__(self, config: Dict[str, Any], tokenizer):
        self.config = config
        self.tokenizer = tokenizer
        self.max_length = config['data'].get('max_length', 512)
        self.max_reasoning_steps = config['model']['thinker'].get('max_reasoning_steps', 16)
        
        # Load all data
        train_file = config['data'].get('train_file', 'data/enhanced_train.jsonl')
        self.all_examples = []
        
        with open(train_file, 'r') as f:
            for line in f:
                data = json.loads(line.strip())
                self.all_examples.append(data)
        
        # Group by complexity
        self.complexity_groups = {
            'easy': [ex for ex in self.all_examples if ex.get('complexity') == 'easy'],
            'medium': [ex for ex in self.all_examples if ex.get('complexity') == 'medium'],
            'hard': [ex for ex in self.all_examples if ex.get('complexity') == 'hard']
        }
        
        print(f"Curriculum data loaded:")
        for complexity, examples in self.complexity_groups.items():
            print(f"  {complexity}: {len(examples)} examples")
    
    def create_curriculum_loader(self, stage: int, total_stages: int = 3) -> DataLoader:
        """Create data loader for specific curriculum stage."""
        if stage == 0:
            # Stage 0: Easy examples only
            examples = self.complexity_groups['easy']
        elif stage == 1:
            # Stage 1: Easy + Medium examples
            examples = self.complexity_groups['easy'] + self.complexity_groups['medium']
        else:
            # Stage 2+: All examples
            examples = self.all_examples
        
        # Create temporary dataset
        class TempDataset(Dataset):
            def __init__(self, examples, parent_loader):
                self.examples = examples
                self.parent = parent_loader
            
            def __len__(self):
                return len(self.examples)
            
            def __getitem__(self, idx):
                example = self.examples[idx]
                
                # Use same processing as ReasoningDataset
                text = example['text']
                reasoning_steps = example.get('reasoning_steps', [])
                complexity = example.get('complexity', 'medium')
                category = example.get('category', 'general')
                
                # Tokenize main text
                encoding = self.parent.tokenizer(
                    text,
                    truncation=True,
                    padding='max_length',
                    max_length=self.parent.max_length,
                    return_tensors='pt'
                )
                
                input_ids = encoding['input_ids'].squeeze(0)
                attention_mask = encoding['attention_mask'].squeeze(0)
                labels = input_ids.clone()
                
                # Process reasoning steps
                reasoning_targets = []
                if reasoning_steps:
                    for step in reasoning_steps[:self.parent.max_reasoning_steps]:
                        step_encoding = self.parent.tokenizer(
                            step,
                            truncation=True,
                            padding='max_length',
                            max_length=50,
                            return_tensors='pt'
                        )
                        reasoning_targets.append(step_encoding['input_ids'].squeeze(0))
                
                use_reasoning = len(reasoning_steps) > 0
                
                complexity_to_decision = {
                    'easy': 0.2,
                    'medium': 0.7,
                    'hard': 0.9
                }
                decision_target = complexity_to_decision.get(complexity, 0.5)
                
                return {
                    'input_ids': input_ids,
                    'attention_mask': attention_mask,
                    'labels': labels,
                    'reasoning_targets': reasoning_targets,
                    'decision_target': torch.tensor(decision_target, dtype=torch.float),
                    'use_reasoning': torch.tensor(use_reasoning, dtype=torch.bool),
                    'complexity': complexity,
                    'category': category,
                    'num_reasoning_steps': len(reasoning_steps)
                }
        
        dataset = TempDataset(examples, self)
        
        print(f"Curriculum stage {stage}: {len(examples)} examples")
        
        return DataLoader(
            dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=True,
            collate_fn=enhanced_collate_fn,
            num_workers=0
        )
