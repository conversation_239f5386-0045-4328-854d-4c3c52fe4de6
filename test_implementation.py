"""
Test script to verify the implementation of LLM with ThinkerModule.
This script performs basic functionality tests without requiring training data.
"""

import sys
import torch
import yaml
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from transformers import AutoTokenizer
from src.models.integrated_model import IntegratedLLM<PERSON>ithThinker


def test_model_initialization():
    """Test model initialization and basic forward pass."""
    print("🧪 Testing model initialization...")

    # Load config
    with open('config/model_config.yaml', 'r') as f:
        full_config = yaml.safe_load(f)
    config = full_config

    # Reduce model size for testing
    config['model']['llm']['hidden_size'] = 256
    config['model']['llm']['num_layers'] = 4
    config['model']['llm']['num_attention_heads'] = 4
    config['model']['thinker']['hidden_size'] = 128
    config['model']['thinker']['num_layers'] = 2

    # Ensure layer_norm_eps is float
    config['model']['llm']['layer_norm_eps'] = float(config['model']['llm']['layer_norm_eps'])
    config['model']['thinker']['layer_norm_eps'] = float(config['model']['thinker']['layer_norm_eps'])

    try:
        model = IntegratedLLMWithThinker(config['model'])
        print("✅ Model initialized successfully")

        # Print model info
        model_info = model.get_model_info()
        print(f"   Total parameters: {model_info['total_params']:,}")
        print(f"   LLM parameters: {model_info['component_info']['llm_params']:,}")
        print(f"   ThinkerModule parameters: {model_info['component_info']['thinker_params']:,}")

        return model, config['model']

    except Exception as e:
        print(f"❌ Model initialization failed: {e}")
        return None, None


def test_forward_pass(model, config):
    """Test forward pass with dummy data."""
    print("\n🧪 Testing forward pass...")

    try:
        # Create dummy input
        batch_size = 2
        seq_len = 10
        vocab_size = config['llm']['vocab_size']

        input_ids = torch.randint(0, vocab_size, (batch_size, seq_len))
        attention_mask = torch.ones(batch_size, seq_len)
        labels = input_ids.clone()

        # Forward pass - test with forced thinking first
        model.eval()
        with torch.no_grad():
            # Test with thinking
            output_thinking = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels,
                force_thinking=True,
                return_reasoning=True
            )

            # Test without thinking
            output_direct = model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                labels=labels,
                force_thinking=False,
                return_reasoning=False
            )

            # Use the thinking output for reporting
            output = output_thinking

        print("✅ Forward pass successful")
        print(f"   Output logits shape: {output['logits'].shape}")
        print(f"   Loss: {output['loss'].item():.4f}")
        print(f"   Thinking mask: {output['thinking_mask']}")

        return True

    except Exception as e:
        print(f"❌ Forward pass failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_generation(model, config):
    """Test text generation."""
    print("\n🧪 Testing text generation...")

    try:
        # Initialize tokenizer
        tokenizer = AutoTokenizer.from_pretrained('gpt2')
        tokenizer.pad_token = tokenizer.eos_token

        # Test prompt
        prompt = "The capital of France is"
        input_ids = tokenizer.encode(prompt, return_tensors='pt')

        model.eval()
        with torch.no_grad():
            # Test with forced thinking
            result_thinking = model.generate(
                input_ids=input_ids,
                max_new_tokens=10,
                temperature=1.0,
                force_thinking=True,
                return_reasoning=True
            )

            # Test without thinking
            result_direct = model.generate(
                input_ids=input_ids,
                max_new_tokens=10,
                temperature=1.0,
                force_thinking=False,
                return_reasoning=False
            )

        print("✅ Text generation successful")

        # Decode results
        generated_thinking = tokenizer.decode(result_thinking['generated_ids'][0], skip_special_tokens=True)
        generated_direct = tokenizer.decode(result_direct['generated_ids'][0], skip_special_tokens=True)

        print(f"   Prompt: {prompt}")
        print(f"   With thinking: {generated_thinking[len(prompt):]}")
        print(f"   Without thinking: {generated_direct[len(prompt):]}")

        return True

    except Exception as e:
        print(f"❌ Text generation failed: {e}")
        return False


def test_decision_mechanism(model):
    """Test decision mechanism functionality."""
    print("\n🧪 Testing decision mechanism...")

    try:
        # Create test inputs with different complexities
        simple_text = "Hello world"
        complex_text = "To solve the quadratic equation ax^2 + bx + c = 0, we use the quadratic formula"

        tokenizer = AutoTokenizer.from_pretrained('gpt2')
        tokenizer.pad_token = tokenizer.eos_token

        simple_ids = tokenizer.encode(simple_text, return_tensors='pt')
        complex_ids = tokenizer.encode(complex_text, return_tensors='pt')

        model.eval()
        with torch.no_grad():
            # Test simple input
            simple_embeddings = model._get_input_embeddings(simple_ids)
            simple_decision = model.decision_mechanism(simple_embeddings, return_features=True)

            # Test complex input
            complex_embeddings = model._get_input_embeddings(complex_ids)
            complex_decision = model.decision_mechanism(complex_embeddings, return_features=True)

        print("✅ Decision mechanism working")
        print(f"   Simple text decision prob: {simple_decision['decision_probs'][0].item():.3f}")
        print(f"   Complex text decision prob: {complex_decision['decision_probs'][0].item():.3f}")
        print(f"   Simple text confidence: {simple_decision['confidence'][0].item():.3f}")
        print(f"   Complex text confidence: {complex_decision['confidence'][0].item():.3f}")

        return True

    except Exception as e:
        print(f"❌ Decision mechanism test failed: {e}")
        return False


def test_thinker_module(model):
    """Test ThinkerModule functionality."""
    print("\n🧪 Testing ThinkerModule...")

    try:
        # Create dummy input
        batch_size = 1
        seq_len = 8
        hidden_size = model.config['llm']['hidden_size']

        input_hidden_states = torch.randn(batch_size, seq_len, hidden_size)
        attention_mask = torch.ones(batch_size, seq_len)

        model.eval()
        with torch.no_grad():
            thinker_output = model.thinker(
                input_hidden_states=input_hidden_states,
                attention_mask=attention_mask,
                generate_steps=True
            )

        print("✅ ThinkerModule working")
        print(f"   Enhanced states shape: {thinker_output['enhanced_hidden_states'].shape}")
        print(f"   Number of reasoning steps: {len(thinker_output['reasoning_steps'])}")
        print(f"   Step attention shapes: {[att.shape for att in thinker_output['step_attentions']]}")

        return True

    except Exception as e:
        print(f"❌ ThinkerModule test failed: {e}")
        return False


def test_projection_layer(model):
    """Test projection layer functionality."""
    print("\n🧪 Testing projection layer...")

    try:
        # Create dummy inputs
        batch_size = 1
        seq_len = 8
        thinker_hidden_size = model.config['thinker']['hidden_size']
        llm_hidden_size = model.config['llm']['hidden_size']

        thinker_states = torch.randn(batch_size, seq_len, thinker_hidden_size)
        llm_states = torch.randn(batch_size, seq_len, llm_hidden_size)
        attention_mask = torch.ones(batch_size, seq_len)

        model.eval()
        with torch.no_grad():
            projected_states = model.projection_layer(
                thinker_states=thinker_states,
                llm_states=llm_states,
                attention_mask=attention_mask
            )

        print("✅ Projection layer working")
        print(f"   Input shape: {thinker_states.shape}")
        print(f"   Output shape: {projected_states.shape}")
        print(f"   Projection info: {model.projection_layer.get_projection_info()}")

        return True

    except Exception as e:
        print(f"❌ Projection layer test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🚀 Starting LLM with ThinkerModule implementation tests...\n")

    # Test model initialization
    model, config = test_model_initialization()
    if model is None:
        print("\n❌ Tests failed at initialization")
        return

    # Run component tests
    tests = [
        ("Forward Pass", lambda: test_forward_pass(model, config)),
        ("Text Generation", lambda: test_generation(model, config)),
        ("Decision Mechanism", lambda: test_decision_mechanism(model)),
        ("ThinkerModule", lambda: test_thinker_module(model)),
        ("Projection Layer", lambda: test_projection_layer(model))
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        if test_func():
            passed += 1

    # Summary
    print(f"\n📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The implementation is working correctly.")
        print("\nNext steps:")
        print("1. Create training data: python examples/train_model.py --create-data")
        print("2. Train the model: python examples/train_model.py")
        print("3. Run inference demo: python examples/inference_demo.py")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

    return passed == total


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
