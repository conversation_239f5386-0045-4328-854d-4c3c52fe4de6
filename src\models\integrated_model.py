"""
Integrated LLM with ThinkerModule - Main architecture combining all components.
Orchestrates the decision-making, reasoning, and generation processes.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any, List, Tuple
import logging

from .llm_model import LLMModel
from .thinker_module import ThinkerModule
from .decision_mechanism import DecisionMechanism
from .projection_layer import ProjectionLayer


class IntegratedLLMWithThinker(nn.Module):
    """
    Integrated architecture combining LLM with ThinkerModule.

    Workflow:
    1. Input → Decision Mechanism → Route decision
    2a. If thinking needed: Input → ThinkerModule → Reasoning + Hidden States → Projection → LLM → Output
    2b. If thinking not needed: Input → LLM directly → Output

    Features:
    - Modular design with clear separation of concerns
    - Efficient routing based on input complexity
    - Transparent reasoning steps for user visibility
    - Memory-efficient (ThinkerModule runs once per input)
    - Configurable thresholds and model sizes
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config

        # Initialize components
        self.llm = LLMModel(config['llm'])

        # Set input hidden size for other components
        thinker_config = config['thinker'].copy()
        thinker_config['input_hidden_size'] = config['llm']['hidden_size']
        thinker_config['vocab_size'] = config['llm']['vocab_size']
        self.thinker = ThinkerModule(thinker_config)

        decision_config = config['decision'].copy()
        decision_config['input_hidden_size'] = config['llm']['hidden_size']
        self.decision_mechanism = DecisionMechanism(decision_config)

        projection_config = config['projection'].copy()
        projection_config['input_size'] = config['thinker']['hidden_size']
        projection_config['output_size'] = config['llm']['hidden_size']
        self.projection_layer = ProjectionLayer(projection_config)

        # Input embedding for initial processing
        self.input_embedding = nn.Embedding(
            config['llm']['vocab_size'],
            config['llm']['hidden_size']
        )
        self.position_embedding = nn.Embedding(
            config['llm']['max_position_embeddings'],
            config['llm']['hidden_size']
        )

        # Training mode flags
        self.training_mode = 'joint'  # 'joint', 'phased', 'component'

        # Statistics tracking
        self.stats = {
            'total_forward_passes': 0,
            'thinker_usage_count': 0,
            'direct_llm_count': 0,
            'avg_reasoning_steps': 0.0
        }

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)

    def _get_input_embeddings(self, input_ids: torch.Tensor) -> torch.Tensor:
        """Get initial input embeddings."""
        batch_size, seq_len = input_ids.shape
        device = input_ids.device

        # Create position IDs
        position_ids = torch.arange(seq_len, device=device).unsqueeze(0).expand(batch_size, -1)

        # Embeddings
        token_embeds = self.input_embedding(input_ids)
        position_embeds = self.position_embedding(position_ids)

        return token_embeds + position_embeds

    def forward(self, input_ids: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                labels: Optional[torch.Tensor] = None,
                force_thinking: Optional[bool] = None,
                return_reasoning: bool = False) -> Dict[str, Any]:
        """
        Forward pass of the integrated model.

        Args:
            input_ids: Token IDs [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
            labels: Target labels for training [batch_size, seq_len]
            force_thinking: Force use of ThinkerModule (None for automatic decision)
            return_reasoning: Whether to return reasoning steps

        Returns:
            Dictionary containing outputs, losses, and optional reasoning steps
        """
        self.stats['total_forward_passes'] += 1

        # Get initial embeddings
        input_embeddings = self._get_input_embeddings(input_ids)

        # Decision phase: determine if thinking is needed
        if force_thinking is None:
            decision_output = self.decision_mechanism(
                input_embeddings, attention_mask, return_features=True
            )
            use_thinking = decision_output['decision']
            decision_info = {
                'decision_logits': decision_output['decision_logits'],
                'decision_probs': decision_output['decision_probs'],
                'confidence': decision_output['confidence'],
                'complexity_features': decision_output['complexity_features']
            }
        else:
            use_thinking = torch.tensor([force_thinking] * input_ids.shape[0],
                                      device=input_ids.device, dtype=torch.float)
            decision_info = {'forced_decision': force_thinking}

        # Process based on decision
        batch_size = input_ids.shape[0]
        thinking_mask = use_thinking.bool()

        # Initialize outputs
        reasoning_outputs = []

        # Initialize final logits tensor
        final_logits = torch.zeros(
            batch_size, input_ids.shape[1], self.config['llm']['vocab_size'],
            device=input_ids.device, dtype=torch.float
        )

        thinking_loss = None
        direct_loss = None

        # Process samples that need thinking
        if thinking_mask.any():
            thinking_indices = thinking_mask.nonzero(as_tuple=True)[0]
            thinking_input_ids = input_ids[thinking_indices]
            thinking_embeddings = input_embeddings[thinking_indices]
            thinking_attention_mask = attention_mask[thinking_indices] if attention_mask is not None else None
            thinking_labels = labels[thinking_indices] if labels is not None else None

            # ThinkerModule processing
            thinker_output = self.thinker(
                thinking_embeddings,
                thinking_attention_mask,
                generate_steps=return_reasoning
            )

            # Project ThinkerModule outputs
            enhanced_states = self.projection_layer(
                thinker_output['enhanced_hidden_states'],
                thinking_embeddings,
                thinking_attention_mask
            )

            # LLM processing with enhanced states
            llm_output = self.llm(
                thinking_input_ids,
                thinking_attention_mask,
                enhanced_hidden_states=enhanced_states,
                labels=thinking_labels
            )

            thinking_logits = llm_output['logits']
            thinking_loss = llm_output['loss']

            # Store in final logits
            final_logits[thinking_indices] = thinking_logits

            # Store reasoning information
            if return_reasoning and 'reasoning_steps' in thinker_output:
                reasoning_outputs.append({
                    'indices': thinking_indices,
                    'reasoning_steps': thinker_output['reasoning_steps'],
                    'step_attentions': thinker_output['step_attentions'],
                    'num_reasoning_steps': thinker_output.get('num_reasoning_steps', len(thinker_output['reasoning_steps'])),
                    'stop_probabilities': thinker_output.get('stop_probabilities', [])
                })

            self.stats['thinker_usage_count'] += len(thinking_indices)

        # Process samples that don't need thinking
        direct_mask = ~thinking_mask
        if direct_mask.any():
            direct_indices = direct_mask.nonzero(as_tuple=True)[0]
            direct_input_ids = input_ids[direct_indices]
            direct_attention_mask = attention_mask[direct_indices] if attention_mask is not None else None
            direct_labels = labels[direct_indices] if labels is not None else None

            # Direct LLM processing
            llm_output = self.llm(
                direct_input_ids,
                direct_attention_mask,
                labels=direct_labels
            )

            direct_logits = llm_output['logits']
            direct_loss = llm_output['loss']

            # Store in final logits
            final_logits[direct_indices] = direct_logits

            self.stats['direct_llm_count'] += len(direct_indices)

        # Combine losses
        total_loss = None
        if labels is not None:
            losses = []
            if thinking_mask.any() and thinking_loss is not None:
                losses.append(thinking_loss * len(thinking_indices))
            if direct_mask.any() and direct_loss is not None:
                losses.append(direct_loss * len(direct_indices))

            if losses:
                total_loss = sum(losses) / batch_size

        # Prepare output
        output = {
            'logits': final_logits,
            'loss': total_loss,
            'decision_info': decision_info,
            'thinking_mask': thinking_mask,
            'stats': self.get_current_stats()
        }

        if reasoning_outputs:
            output['reasoning_outputs'] = reasoning_outputs

        return output

    def generate(self, input_ids: torch.Tensor,
                 max_new_tokens: int = 50,
                 temperature: float = 1.0,
                 top_p: float = 0.9,
                 force_thinking: Optional[bool] = None,
                 return_reasoning: bool = False) -> Dict[str, Any]:
        """
        Generate text with optional reasoning.

        Args:
            input_ids: Input token IDs [batch_size, seq_len]
            max_new_tokens: Maximum new tokens to generate
            temperature: Sampling temperature
            top_p: Top-p sampling threshold
            force_thinking: Force use of ThinkerModule
            return_reasoning: Whether to return reasoning steps

        Returns:
            Dictionary with generated tokens and optional reasoning
        """
        self.eval()

        with torch.no_grad():
            # Initial forward pass to determine thinking and get reasoning
            initial_output = self.forward(
                input_ids,
                force_thinking=force_thinking,
                return_reasoning=return_reasoning
            )

            thinking_mask = initial_output['thinking_mask']
            enhanced_states = None
            reasoning_info = None

            # If thinking was used, get enhanced states for generation
            if thinking_mask.any():
                # Re-run ThinkerModule to get enhanced states for generation
                input_embeddings = self._get_input_embeddings(input_ids)
                thinker_output = self.thinker(input_embeddings, generate_steps=return_reasoning)
                enhanced_states = self.projection_layer(
                    thinker_output['enhanced_hidden_states'],
                    input_embeddings
                )

                if return_reasoning:
                    reasoning_info = {
                        'reasoning_steps': thinker_output['reasoning_steps'],
                        'step_attentions': thinker_output['step_attentions']
                    }

            # Generate with LLM (simplified - no enhanced states during generation for now)
            generated_ids = self.llm.generate(
                input_ids,
                max_new_tokens=max_new_tokens,
                temperature=temperature,
                top_p=top_p,
                enhanced_hidden_states=None
            )

        result = {
            'generated_ids': generated_ids,
            'thinking_used': thinking_mask,
            'decision_info': initial_output['decision_info']
        }

        if reasoning_info:
            result['reasoning_info'] = reasoning_info

        return result

    def get_current_stats(self) -> Dict[str, float]:
        """Get current usage statistics."""
        total = self.stats['total_forward_passes']
        if total == 0:
            return self.stats.copy()

        return {
            'total_forward_passes': total,
            'thinker_usage_rate': self.stats['thinker_usage_count'] / total,
            'direct_llm_rate': self.stats['direct_llm_count'] / total,
            'avg_reasoning_steps': self.stats['avg_reasoning_steps']
        }

    def reset_stats(self):
        """Reset usage statistics."""
        self.stats = {
            'total_forward_passes': 0,
            'thinker_usage_count': 0,
            'direct_llm_count': 0,
            'avg_reasoning_steps': 0.0
        }

    def set_decision_threshold(self, threshold: float):
        """Update decision threshold."""
        self.decision_mechanism.set_threshold(threshold)

    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information."""
        return {
            'config': self.config,
            'stats': self.get_current_stats(),
            'component_info': {
                'llm_params': sum(p.numel() for p in self.llm.parameters()),
                'thinker_params': sum(p.numel() for p in self.thinker.parameters()),
                'decision_params': sum(p.numel() for p in self.decision_mechanism.parameters()),
                'projection_params': sum(p.numel() for p in self.projection_layer.parameters()),
            },
            'total_params': sum(p.numel() for p in self.parameters())
        }
