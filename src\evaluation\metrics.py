"""
Evaluation metrics for Integrated LLM with ThinkerModule.
Includes metrics for language modeling, reasoning quality, and decision accuracy.
"""

import torch
import torch.nn.functional as F
from typing import Dict, List, Any, Optional
import numpy as np
from collections import defaultdict
import math


class PerplexityMetric:
    """
    Perplexity metric for language modeling evaluation.
    """
    
    def __init__(self):
        self.total_loss = 0.0
        self.total_tokens = 0
        
    def update(self, logits: torch.Tensor, labels: torch.Tensor, 
               attention_mask: Optional[torch.Tensor] = None):
        """
        Update metric with batch results.
        
        Args:
            logits: Model logits [batch_size, seq_len, vocab_size]
            labels: Target labels [batch_size, seq_len]
            attention_mask: Attention mask [batch_size, seq_len]
        """
        # Shift for next token prediction
        shift_logits = logits[..., :-1, :].contiguous()
        shift_labels = labels[..., 1:].contiguous()
        
        if attention_mask is not None:
            shift_mask = attention_mask[..., 1:].contiguous()
        else:
            shift_mask = torch.ones_like(shift_labels)
        
        # Calculate loss per token
        loss_fct = torch.nn.CrossEntropyLoss(reduction='none')
        losses = loss_fct(
            shift_logits.view(-1, shift_logits.size(-1)),
            shift_labels.view(-1)
        )
        losses = losses.view(shift_labels.shape)
        
        # Apply mask and sum
        masked_losses = losses * shift_mask
        self.total_loss += masked_losses.sum().item()
        self.total_tokens += shift_mask.sum().item()
    
    def compute(self) -> float:
        """Compute perplexity."""
        if self.total_tokens == 0:
            return float('inf')
        
        avg_loss = self.total_loss / self.total_tokens
        return math.exp(avg_loss)
    
    def reset(self):
        """Reset metric state."""
        self.total_loss = 0.0
        self.total_tokens = 0


class ReasoningQualityMetric:
    """
    Metric for evaluating reasoning quality.
    Measures coherence, relevance, and completeness of reasoning steps.
    """
    
    def __init__(self):
        self.reasoning_scores = []
        self.step_counts = []
        
    def update(self, reasoning_steps: List[torch.Tensor], 
               target_reasoning: Optional[List[torch.Tensor]] = None):
        """
        Update metric with reasoning steps.
        
        Args:
            reasoning_steps: Generated reasoning steps
            target_reasoning: Target reasoning steps (optional)
        """
        if not reasoning_steps:
            return
        
        # Count reasoning steps
        self.step_counts.append(len(reasoning_steps))
        
        if target_reasoning is not None:
            # Supervised evaluation
            score = self._compute_supervised_score(reasoning_steps, target_reasoning)
        else:
            # Unsupervised evaluation based on coherence
            score = self._compute_coherence_score(reasoning_steps)
        
        self.reasoning_scores.append(score)
    
    def _compute_supervised_score(self, generated: List[torch.Tensor], 
                                 target: List[torch.Tensor]) -> float:
        """Compute supervised reasoning score."""
        if not generated or not target:
            return 0.0
        
        # Simple token-level similarity
        total_similarity = 0.0
        count = 0
        
        for gen_step, tgt_step in zip(generated, target):
            # Convert to probabilities
            gen_probs = F.softmax(gen_step, dim=-1)
            tgt_probs = F.one_hot(tgt_step, num_classes=gen_step.size(-1)).float()
            
            # Compute KL divergence (lower is better)
            kl_div = F.kl_div(gen_probs.log(), tgt_probs, reduction='mean')
            similarity = torch.exp(-kl_div).item()
            
            total_similarity += similarity
            count += 1
        
        return total_similarity / count if count > 0 else 0.0
    
    def _compute_coherence_score(self, reasoning_steps: List[torch.Tensor]) -> float:
        """Compute unsupervised coherence score."""
        if len(reasoning_steps) < 2:
            return 0.5  # Neutral score for single or no steps
        
        # Measure consistency between consecutive steps
        coherence_scores = []
        
        for i in range(len(reasoning_steps) - 1):
            step1 = F.softmax(reasoning_steps[i], dim=-1)
            step2 = F.softmax(reasoning_steps[i + 1], dim=-1)
            
            # Compute cosine similarity between step distributions
            similarity = F.cosine_similarity(
                step1.view(-1), step2.view(-1), dim=0
            ).item()
            
            coherence_scores.append(max(0, similarity))  # Clamp to positive
        
        return np.mean(coherence_scores) if coherence_scores else 0.0
    
    def compute(self) -> Dict[str, float]:
        """Compute reasoning quality metrics."""
        if not self.reasoning_scores:
            return {
                'avg_reasoning_score': 0.0,
                'avg_reasoning_steps': 0.0,
                'reasoning_consistency': 0.0
            }
        
        return {
            'avg_reasoning_score': np.mean(self.reasoning_scores),
            'avg_reasoning_steps': np.mean(self.step_counts),
            'reasoning_consistency': np.std(self.reasoning_scores)
        }
    
    def reset(self):
        """Reset metric state."""
        self.reasoning_scores = []
        self.step_counts = []


class DecisionAccuracyMetric:
    """
    Metric for evaluating decision mechanism accuracy.
    """
    
    def __init__(self):
        self.predictions = []
        self.targets = []
        self.confidences = []
        
    def update(self, decision_probs: torch.Tensor, 
               target_decisions: torch.Tensor,
               confidence: Optional[torch.Tensor] = None):
        """
        Update metric with decision results.
        
        Args:
            decision_probs: Decision probabilities [batch_size]
            target_decisions: Target decisions [batch_size]
            confidence: Confidence scores [batch_size] (optional)
        """
        # Convert probabilities to binary decisions
        predictions = (decision_probs > 0.5).float()
        
        self.predictions.extend(predictions.cpu().numpy())
        self.targets.extend(target_decisions.cpu().numpy())
        
        if confidence is not None:
            self.confidences.extend(confidence.cpu().numpy())
    
    def compute(self) -> Dict[str, float]:
        """Compute decision accuracy metrics."""
        if not self.predictions:
            return {
                'accuracy': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'avg_confidence': 0.0
            }
        
        predictions = np.array(self.predictions)
        targets = np.array(self.targets)
        
        # Basic metrics
        accuracy = np.mean(predictions == targets)
        
        # Precision, recall, F1 for positive class (thinking needed)
        tp = np.sum((predictions == 1) & (targets == 1))
        fp = np.sum((predictions == 1) & (targets == 0))
        fn = np.sum((predictions == 0) & (targets == 1))
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
        
        # Average confidence
        avg_confidence = np.mean(self.confidences) if self.confidences else 0.0
        
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1_score,
            'avg_confidence': avg_confidence
        }
    
    def reset(self):
        """Reset metric state."""
        self.predictions = []
        self.targets = []
        self.confidences = []


class EfficiencyMetric:
    """
    Metric for evaluating computational efficiency.
    """
    
    def __init__(self):
        self.thinking_times = []
        self.direct_times = []
        self.thinking_counts = 0
        self.direct_counts = 0
        
    def update(self, processing_time: float, used_thinking: bool):
        """
        Update metric with processing time.
        
        Args:
            processing_time: Time taken for processing
            used_thinking: Whether ThinkerModule was used
        """
        if used_thinking:
            self.thinking_times.append(processing_time)
            self.thinking_counts += 1
        else:
            self.direct_times.append(processing_time)
            self.direct_counts += 1
    
    def compute(self) -> Dict[str, float]:
        """Compute efficiency metrics."""
        total_samples = self.thinking_counts + self.direct_counts
        
        if total_samples == 0:
            return {
                'avg_thinking_time': 0.0,
                'avg_direct_time': 0.0,
                'thinking_usage_rate': 0.0,
                'efficiency_ratio': 1.0
            }
        
        avg_thinking_time = np.mean(self.thinking_times) if self.thinking_times else 0.0
        avg_direct_time = np.mean(self.direct_times) if self.direct_times else 0.0
        thinking_usage_rate = self.thinking_counts / total_samples
        
        # Efficiency ratio: how much slower thinking is compared to direct
        efficiency_ratio = avg_thinking_time / avg_direct_time if avg_direct_time > 0 else 1.0
        
        return {
            'avg_thinking_time': avg_thinking_time,
            'avg_direct_time': avg_direct_time,
            'thinking_usage_rate': thinking_usage_rate,
            'efficiency_ratio': efficiency_ratio
        }
    
    def reset(self):
        """Reset metric state."""
        self.thinking_times = []
        self.direct_times = []
        self.thinking_counts = 0
        self.direct_counts = 0


class CombinedMetrics:
    """
    Combined metrics for comprehensive evaluation.
    """
    
    def __init__(self):
        self.perplexity = PerplexityMetric()
        self.reasoning_quality = ReasoningQualityMetric()
        self.decision_accuracy = DecisionAccuracyMetric()
        self.efficiency = EfficiencyMetric()
        
    def update(self, model_output: Dict[str, Any], 
               labels: torch.Tensor,
               target_decisions: Optional[torch.Tensor] = None,
               target_reasoning: Optional[List[torch.Tensor]] = None,
               processing_time: Optional[float] = None):
        """Update all metrics with model output."""
        
        # Perplexity
        self.perplexity.update(
            model_output['logits'], 
            labels, 
            model_output.get('attention_mask')
        )
        
        # Reasoning quality
        if 'reasoning_outputs' in model_output:
            for reasoning_output in model_output['reasoning_outputs']:
                self.reasoning_quality.update(
                    reasoning_output['reasoning_steps'],
                    target_reasoning
                )
        
        # Decision accuracy
        decision_info = model_output['decision_info']
        if 'decision_probs' in decision_info and target_decisions is not None:
            self.decision_accuracy.update(
                decision_info['decision_probs'].squeeze(-1),
                target_decisions,
                decision_info.get('confidence')
            )
        
        # Efficiency
        if processing_time is not None:
            thinking_mask = model_output['thinking_mask']
            for i, used_thinking in enumerate(thinking_mask):
                self.efficiency.update(processing_time, used_thinking.item())
    
    def compute(self) -> Dict[str, float]:
        """Compute all metrics."""
        results = {}
        
        # Perplexity
        results['perplexity'] = self.perplexity.compute()
        
        # Reasoning quality
        reasoning_metrics = self.reasoning_quality.compute()
        for key, value in reasoning_metrics.items():
            results[f'reasoning_{key}'] = value
        
        # Decision accuracy
        decision_metrics = self.decision_accuracy.compute()
        for key, value in decision_metrics.items():
            results[f'decision_{key}'] = value
        
        # Efficiency
        efficiency_metrics = self.efficiency.compute()
        for key, value in efficiency_metrics.items():
            results[f'efficiency_{key}'] = value
        
        return results
    
    def reset(self):
        """Reset all metrics."""
        self.perplexity.reset()
        self.reasoning_quality.reset()
        self.decision_accuracy.reset()
        self.efficiency.reset()
