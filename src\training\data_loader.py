"""
Data Loading utilities for Integrated LLM with ThinkerModule.
Handles various data formats and preprocessing for training.
"""

import torch
from torch.utils.data import Dataset, DataLoader as TorchDataLoader
from transformers import AutoTokenizer
from typing import Dict, Any, List, Optional, Union
import json
import logging
from pathlib import Path


class TextDataset(Dataset):
    """
    Dataset for text data with optional reasoning annotations.
    Supports both simple text and complex reasoning tasks.
    """
    
    def __init__(self, data_path: str, tokenizer: AutoTokenizer, 
                 max_length: int = 1024, include_reasoning: bool = False):
        self.data_path = data_path
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.include_reasoning = include_reasoning
        
        # Load data
        self.data = self._load_data()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Loaded {len(self.data)} samples from {data_path}")
        
    def _load_data(self) -> List[Dict[str, Any]]:
        """Load data from file."""
        data = []
        
        if self.data_path.endswith('.jsonl'):
            with open(self.data_path, 'r', encoding='utf-8') as f:
                for line in f:
                    data.append(json.loads(line.strip()))
        elif self.data_path.endswith('.json'):
            with open(self.data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        else:
            # Plain text file
            with open(self.data_path, 'r', encoding='utf-8') as f:
                text = f.read()
                # Split into chunks
                chunks = text.split('\n\n')  # Assume paragraphs are separated by double newlines
                data = [{'text': chunk.strip()} for chunk in chunks if chunk.strip()]
        
        return data
    
    def __len__(self) -> int:
        return len(self.data)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a single data sample."""
        item = self.data[idx]
        
        # Extract text
        if 'text' in item:
            text = item['text']
        elif 'input' in item and 'output' in item:
            text = f"{item['input']}\n{item['output']}"
        else:
            raise ValueError(f"Invalid data format at index {idx}")
        
        # Tokenize
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        result = {
            'input_ids': encoding['input_ids'].squeeze(0),
            'attention_mask': encoding['attention_mask'].squeeze(0),
            'labels': encoding['input_ids'].squeeze(0).clone()  # For language modeling
        }
        
        # Add reasoning information if available
        if self.include_reasoning and 'reasoning' in item:
            # Tokenize reasoning steps
            reasoning_steps = []
            for step in item['reasoning']:
                step_encoding = self.tokenizer(
                    step,
                    truncation=True,
                    max_length=50,  # Shorter for reasoning steps
                    return_tensors='pt'
                )
                reasoning_steps.append(step_encoding['input_ids'].squeeze(0))
            
            result['target_reasoning'] = reasoning_steps
        
        # Add decision target if available
        if 'needs_thinking' in item:
            result['target_decisions'] = torch.tensor(float(item['needs_thinking']))
        
        return result


class ReasoningDataset(Dataset):
    """
    Specialized dataset for reasoning tasks with explicit thinking requirements.
    """
    
    def __init__(self, data_path: str, tokenizer: AutoTokenizer, max_length: int = 1024):
        self.data_path = data_path
        self.tokenizer = tokenizer
        self.max_length = max_length
        
        self.data = self._load_reasoning_data()
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Loaded {len(self.data)} reasoning samples from {data_path}")
    
    def _load_reasoning_data(self) -> List[Dict[str, Any]]:
        """Load reasoning-specific data."""
        with open(self.data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Validate data format
        for item in data:
            required_fields = ['question', 'answer', 'reasoning_steps', 'complexity']
            if not all(field in item for field in required_fields):
                raise ValueError(f"Missing required fields in reasoning data: {required_fields}")
        
        return data
    
    def __len__(self) -> int:
        return len(self.data)
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a reasoning sample."""
        item = self.data[idx]
        
        # Combine question and answer
        full_text = f"Question: {item['question']}\nAnswer: {item['answer']}"
        
        # Tokenize main text
        encoding = self.tokenizer(
            full_text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        result = {
            'input_ids': encoding['input_ids'].squeeze(0),
            'attention_mask': encoding['attention_mask'].squeeze(0),
            'labels': encoding['input_ids'].squeeze(0).clone()
        }
        
        # Add reasoning steps
        reasoning_steps = []
        for step in item['reasoning_steps']:
            step_encoding = self.tokenizer(
                step,
                truncation=True,
                max_length=50,
                return_tensors='pt'
            )
            reasoning_steps.append(step_encoding['input_ids'].squeeze(0))
        
        result['target_reasoning'] = reasoning_steps
        
        # Add decision target based on complexity
        complexity = item['complexity']
        needs_thinking = complexity in ['high', 'very_high']
        result['target_decisions'] = torch.tensor(float(needs_thinking))
        
        return result


def collate_fn(batch: List[Dict[str, torch.Tensor]]) -> Dict[str, torch.Tensor]:
    """
    Custom collate function to handle variable-length reasoning steps.
    """
    # Standard fields
    input_ids = torch.stack([item['input_ids'] for item in batch])
    attention_mask = torch.stack([item['attention_mask'] for item in batch])
    labels = torch.stack([item['labels'] for item in batch])
    
    result = {
        'input_ids': input_ids,
        'attention_mask': attention_mask,
        'labels': labels
    }
    
    # Handle target decisions
    if 'target_decisions' in batch[0]:
        target_decisions = torch.stack([item['target_decisions'] for item in batch])
        result['target_decisions'] = target_decisions
    
    # Handle target reasoning (more complex due to variable lengths)
    if 'target_reasoning' in batch[0]:
        # For now, we'll skip this in collation and handle it in the trainer
        # This is because reasoning steps can have different numbers per sample
        pass
    
    return result


class DataLoader:
    """
    Main data loader class that handles different data types and formats.
    """
    
    def __init__(self, config: Dict[str, Any], tokenizer: AutoTokenizer):
        self.config = config
        self.tokenizer = tokenizer
        self.logger = logging.getLogger(__name__)
        
    def create_train_loader(self) -> TorchDataLoader:
        """Create training data loader."""
        train_file = self.config['data']['train_file']
        
        if not Path(train_file).exists():
            raise FileNotFoundError(f"Training file not found: {train_file}")
        
        # Determine dataset type based on file content
        if self._is_reasoning_dataset(train_file):
            dataset = ReasoningDataset(
                data_path=train_file,
                tokenizer=self.tokenizer,
                max_length=self.config['data']['max_length']
            )
        else:
            dataset = TextDataset(
                data_path=train_file,
                tokenizer=self.tokenizer,
                max_length=self.config['data']['max_length'],
                include_reasoning=True
            )
        
        return TorchDataLoader(
            dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=True,
            collate_fn=collate_fn,
            num_workers=self.config['training'].get('num_workers', 0)
        )
    
    def create_eval_loader(self) -> Optional[TorchDataLoader]:
        """Create evaluation data loader."""
        eval_file = self.config['data'].get('eval_file')
        
        if not eval_file or not Path(eval_file).exists():
            self.logger.warning("Evaluation file not found, skipping evaluation")
            return None
        
        # Determine dataset type
        if self._is_reasoning_dataset(eval_file):
            dataset = ReasoningDataset(
                data_path=eval_file,
                tokenizer=self.tokenizer,
                max_length=self.config['data']['max_length']
            )
        else:
            dataset = TextDataset(
                data_path=eval_file,
                tokenizer=self.tokenizer,
                max_length=self.config['data']['max_length'],
                include_reasoning=True
            )
        
        return TorchDataLoader(
            dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=self.config['training'].get('num_workers', 0)
        )
    
    def create_test_loader(self) -> Optional[TorchDataLoader]:
        """Create test data loader."""
        test_file = self.config['data'].get('test_file')
        
        if not test_file or not Path(test_file).exists():
            return None
        
        # Determine dataset type
        if self._is_reasoning_dataset(test_file):
            dataset = ReasoningDataset(
                data_path=test_file,
                tokenizer=self.tokenizer,
                max_length=self.config['data']['max_length']
            )
        else:
            dataset = TextDataset(
                data_path=test_file,
                tokenizer=self.tokenizer,
                max_length=self.config['data']['max_length'],
                include_reasoning=True
            )
        
        return TorchDataLoader(
            dataset,
            batch_size=self.config['training']['batch_size'],
            shuffle=False,
            collate_fn=collate_fn,
            num_workers=self.config['training'].get('num_workers', 0)
        )
    
    def _is_reasoning_dataset(self, file_path: str) -> bool:
        """Check if the dataset contains reasoning annotations."""
        try:
            if file_path.endswith('.jsonl'):
                with open(file_path, 'r') as f:
                    first_line = f.readline()
                    sample = json.loads(first_line)
            else:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    sample = data[0] if isinstance(data, list) else data
            
            # Check for reasoning-specific fields
            reasoning_fields = ['reasoning_steps', 'complexity', 'question', 'answer']
            return any(field in sample for field in reasoning_fields)
        
        except (json.JSONDecodeError, IndexError, KeyError):
            return False
