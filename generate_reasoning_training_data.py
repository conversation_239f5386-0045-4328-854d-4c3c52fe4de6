"""
Generate high-quality reasoning training data for the enhanced ThinkerModule.
This creates diverse examples with step-by-step reasoning.
"""

import json
import random
import math
from typing import List, Dict, Any


class ReasoningDataGenerator:
    """Generate diverse reasoning training examples."""
    
    def __init__(self):
        self.examples = []
    
    def generate_math_problems(self, num_examples: int = 100) -> List[Dict[str, Any]]:
        """Generate mathematical reasoning problems."""
        examples = []
        
        # Linear equations
        for _ in range(num_examples // 4):
            a = random.randint(2, 10)
            b = random.randint(1, 20)
            c = random.randint(10, 50)
            x = (c - b) // a
            
            if a * x + b == c:  # Ensure integer solution
                problem = f"Solve the equation {a}x + {b} = {c}"
                reasoning_steps = [
                    f"I need to solve for x in the equation {a}x + {b} = {c}",
                    f"First, I'll subtract {b} from both sides: {a}x = {c - b}",
                    f"Then I'll divide both sides by {a}: x = {(c - b) // a}",
                    f"Let me verify: {a} × {x} + {b} = {a * x + b} = {c} ✓"
                ]
                
                answer = f"To solve {a}x + {b} = {c}, I need to isolate x. Subtracting {b} from both sides gives {a}x = {c - b}. Dividing by {a} gives x = {x}."
                
                examples.append({
                    "text": problem + " " + answer,
                    "reasoning_steps": reasoning_steps,
                    "complexity": "medium",
                    "category": "math"
                })
        
        # Distance/speed/time problems
        for _ in range(num_examples // 4):
            speed = random.randint(30, 80)
            time = random.randint(2, 6)
            distance = speed * time
            
            problem = f"If a car travels {speed} miles per hour for {time} hours, how far does it travel?"
            reasoning_steps = [
                f"I need to find the distance traveled using the formula: distance = speed × time",
                f"Given: speed = {speed} mph, time = {time} hours",
                f"Distance = {speed} × {time} = {distance} miles",
                f"Therefore, the car travels {distance} miles"
            ]
            
            answer = f"Using the formula distance = speed × time, I calculate {speed} × {time} = {distance} miles."
            
            examples.append({
                "text": problem + " " + answer,
                "reasoning_steps": reasoning_steps,
                "complexity": "medium",
                "category": "math"
            })
        
        # Area calculations
        for _ in range(num_examples // 4):
            radius = random.randint(3, 15)
            area = round(math.pi * radius * radius, 2)
            
            problem = f"Calculate the area of a circle with radius {radius} units"
            reasoning_steps = [
                f"I need to find the area of a circle with radius {radius} units",
                f"The formula for the area of a circle is A = πr²",
                f"Substituting r = {radius}: A = π × {radius}²",
                f"A = π × {radius * radius} = {area} square units"
            ]
            
            answer = f"Using the formula A = πr², the area is π × {radius}² = {area} square units."
            
            examples.append({
                "text": problem + " " + answer,
                "reasoning_steps": reasoning_steps,
                "complexity": "medium",
                "category": "math"
            })
        
        # Percentage problems
        for _ in range(num_examples // 4):
            total = random.randint(100, 1000)
            percentage = random.randint(10, 50)
            result = (total * percentage) // 100
            
            problem = f"What is {percentage}% of {total}?"
            reasoning_steps = [
                f"I need to calculate {percentage}% of {total}",
                f"To find a percentage, I multiply the number by the percentage and divide by 100",
                f"{percentage}% of {total} = ({percentage} × {total}) ÷ 100",
                f"= {percentage * total} ÷ 100 = {result}"
            ]
            
            answer = f"To find {percentage}% of {total}, I calculate ({percentage} × {total}) ÷ 100 = {result}."
            
            examples.append({
                "text": problem + " " + answer,
                "reasoning_steps": reasoning_steps,
                "complexity": "easy",
                "category": "math"
            })
        
        return examples
    
    def generate_logic_problems(self, num_examples: int = 50) -> List[Dict[str, Any]]:
        """Generate logical reasoning problems."""
        examples = []
        
        # Syllogisms
        syllogisms = [
            {
                "premise1": "All birds can fly",
                "premise2": "Penguins are birds",
                "question": "Can penguins fly?",
                "reasoning": [
                    "I have two premises: 'All birds can fly' and 'Penguins are birds'",
                    "If I follow the logic strictly, penguins should be able to fly",
                    "However, this creates a contradiction with real-world knowledge",
                    "The first premise 'All birds can fly' is actually false",
                    "Therefore, the conclusion that penguins can fly is incorrect"
                ],
                "answer": "No, penguins cannot fly. The premise 'All birds can fly' is false, as some birds like penguins, ostriches, and emus are flightless."
            },
            {
                "premise1": "All roses are flowers",
                "premise2": "Some flowers are red",
                "question": "Are some roses red?",
                "reasoning": [
                    "I have: 'All roses are flowers' and 'Some flowers are red'",
                    "From the first premise, roses are a subset of flowers",
                    "From the second premise, some flowers have the property of being red",
                    "It's possible that some roses are among the red flowers",
                    "Therefore, some roses can be red"
                ],
                "answer": "Yes, some roses are red. Since all roses are flowers and some flowers are red, it's logical that some roses could be red."
            }
        ]
        
        for syllogism in syllogisms:
            for _ in range(num_examples // len(syllogisms)):
                problem = f"Given: {syllogism['premise1']}. {syllogism['premise2']}. {syllogism['question']}"
                
                examples.append({
                    "text": problem + " " + syllogism['answer'],
                    "reasoning_steps": syllogism['reasoning'],
                    "complexity": "hard",
                    "category": "logic"
                })
        
        return examples
    
    def generate_science_problems(self, num_examples: int = 30) -> List[Dict[str, Any]]:
        """Generate science reasoning problems."""
        examples = []
        
        # Photosynthesis
        problem = "Explain the basic process of photosynthesis"
        reasoning_steps = [
            "Photosynthesis is the process by which plants make their own food",
            "It requires three main inputs: sunlight, carbon dioxide, and water",
            "The process occurs mainly in the leaves, specifically in chloroplasts",
            "Chlorophyll captures light energy from the sun",
            "This energy converts CO₂ and H₂O into glucose (C₆H₁₂O₆) and oxygen",
            "The chemical equation is: 6CO₂ + 6H₂O + light energy → C₆H₁₂O₆ + 6O₂"
        ]
        answer = "Photosynthesis is the process where plants use sunlight, carbon dioxide, and water to produce glucose and oxygen. The reaction occurs in chloroplasts using chlorophyll to capture light energy."
        
        for _ in range(num_examples // 3):
            examples.append({
                "text": problem + " " + answer,
                "reasoning_steps": reasoning_steps,
                "complexity": "hard",
                "category": "science"
            })
        
        # Water cycle
        problem = "Describe how the water cycle works"
        reasoning_steps = [
            "The water cycle is the continuous movement of water on Earth",
            "It starts with evaporation: sun heats water in oceans, lakes, and rivers",
            "Water vapor rises into the atmosphere",
            "As it rises higher, it cools and condenses into tiny droplets forming clouds",
            "When clouds become heavy, precipitation occurs (rain, snow, sleet)",
            "Water returns to Earth's surface and flows back to water bodies",
            "The cycle then repeats continuously"
        ]
        answer = "The water cycle involves evaporation of water into vapor, condensation into clouds, precipitation back to Earth, and collection in water bodies, creating a continuous cycle."
        
        for _ in range(num_examples // 3):
            examples.append({
                "text": problem + " " + answer,
                "reasoning_steps": reasoning_steps,
                "complexity": "medium",
                "category": "science"
            })
        
        return examples
    
    def generate_simple_facts(self, num_examples: int = 50) -> List[Dict[str, Any]]:
        """Generate simple factual questions that don't need reasoning."""
        examples = []
        
        simple_facts = [
            ("What is the capital of France?", "The capital of France is Paris."),
            ("What is 2 + 2?", "2 + 2 equals 4."),
            ("What color is the sky?", "The sky is blue."),
            ("How many days are in a week?", "There are 7 days in a week."),
            ("What is the largest planet in our solar system?", "Jupiter is the largest planet in our solar system."),
        ]
        
        for question, answer in simple_facts:
            for _ in range(num_examples // len(simple_facts)):
                examples.append({
                    "text": question + " " + answer,
                    "reasoning_steps": [],  # No reasoning needed for simple facts
                    "complexity": "easy",
                    "category": "facts"
                })
        
        return examples
    
    def generate_all_data(self) -> List[Dict[str, Any]]:
        """Generate comprehensive training dataset."""
        all_examples = []
        
        # Generate different types of problems
        all_examples.extend(self.generate_math_problems(200))
        all_examples.extend(self.generate_logic_problems(100))
        all_examples.extend(self.generate_science_problems(60))
        all_examples.extend(self.generate_simple_facts(100))
        
        # Shuffle the data
        random.shuffle(all_examples)
        
        return all_examples
    
    def save_to_files(self, examples: List[Dict[str, Any]], train_ratio: float = 0.8):
        """Save examples to training and evaluation files."""
        # Split into train/eval
        split_idx = int(len(examples) * train_ratio)
        train_examples = examples[:split_idx]
        eval_examples = examples[split_idx:]
        
        # Save training data
        with open('data/enhanced_train.jsonl', 'w') as f:
            for example in train_examples:
                f.write(json.dumps(example) + '\n')
        
        # Save evaluation data
        with open('data/enhanced_eval.jsonl', 'w') as f:
            for example in eval_examples:
                f.write(json.dumps(example) + '\n')
        
        print(f"Generated {len(train_examples)} training examples")
        print(f"Generated {len(eval_examples)} evaluation examples")
        print(f"Total: {len(examples)} examples")
        
        # Print statistics
        categories = {}
        complexities = {}
        for example in examples:
            cat = example['category']
            comp = example['complexity']
            categories[cat] = categories.get(cat, 0) + 1
            complexities[comp] = complexities.get(comp, 0) + 1
        
        print(f"\nCategories: {categories}")
        print(f"Complexities: {complexities}")


def main():
    """Generate and save reasoning training data."""
    print("Generating high-quality reasoning training data...")
    
    generator = ReasoningDataGenerator()
    examples = generator.generate_all_data()
    
    # Create data directory if it doesn't exist
    import os
    os.makedirs('data', exist_ok=True)
    
    generator.save_to_files(examples)
    print("Training data generation completed!")


if __name__ == '__main__':
    main()
