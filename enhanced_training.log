2025-05-29 13:50:20,734 - __main__ - INFO - Starting enhanced ThinkerModule training...
2025-05-29 13:50:20,734 - __main__ - INFO - Enhanced configuration created
2025-05-29 13:50:20,734 - __main__ - INFO - Training data validation passed: data/train.jsonl
2025-05-29 13:50:20,734 - __main__ - INFO - Initializing tokenizer...
2025-05-29 13:50:21,403 - __main__ - INFO - Initializing enhanced model...
2025-05-29 13:50:23,427 - __main__ - INFO - Enhanced model initialized with 328,636,311 parameters
2025-05-29 13:50:23,427 - __main__ - INFO -   Base LLM: 124,439,808
2025-05-29 13:50:23,427 - __main__ - INFO -   Enhanced ThinkerModule: 175,024,724
2025-05-29 13:50:23,427 - __main__ - INFO -   Enhanced Decision Mechanism: 12,637,507
2025-05-29 13:50:23,427 - __main__ - INFO -   Integration Components: 16,534,272
2025-05-29 13:50:23,427 - __main__ - INFO - Creating data loaders...
2025-05-29 13:50:23,431 - src.training.data_loader - INFO - Loaded 288 samples from data/train.jsonl
2025-05-29 13:50:23,431 - src.training.data_loader - INFO - Loaded 22 samples from data/eval.jsonl
2025-05-29 13:50:23,431 - __main__ - INFO - Training batches: 72
2025-05-29 13:50:23,431 - __main__ - INFO - Evaluation batches: 6
2025-05-29 13:50:23,431 - __main__ - INFO - Initializing enhanced trainer...
2025-05-29 13:50:25,598 - __main__ - INFO - Enhanced Training Configuration:
2025-05-29 13:50:25,600 - __main__ - INFO -   Batch size: 4
2025-05-29 13:50:25,600 - __main__ - INFO -   Gradient accumulation: 2
2025-05-29 13:50:25,600 - __main__ - INFO -   Learning rate: 5e-05
2025-05-29 13:50:25,600 - __main__ - INFO -   Number of epochs: 5
2025-05-29 13:50:25,600 - __main__ - INFO -   Enhanced loss weights:
2025-05-29 13:50:25,600 - __main__ - INFO -     LLM: 1.0
2025-05-29 13:50:25,600 - __main__ - INFO -     Thinker: 0.1
2025-05-29 13:50:25,600 - __main__ - INFO -     Decision: 0.15
2025-05-29 13:50:25,600 - __main__ - INFO -     Projection: 0.05
2025-05-29 13:50:25,600 - __main__ - INFO -   Progressive weighting: True
2025-05-29 13:50:25,600 - __main__ - INFO - Starting enhanced training...
2025-05-29 13:50:25,600 - src.training.enhanced_trainer - INFO - Starting enhanced training...
2025-05-29 13:50:26,112 - __main__ - ERROR - Enhanced training failed with error: Expected attn_mask dtype to be bool or float or to match query dtype, but got attn_mask.dtype: __int64 and  query.dtype: float instead.
2025-05-29 13:50:26,115 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\train_enhanced_model.py", line 185, in main
    history = trainer.train(train_loader, eval_loader)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\enhanced_trainer.py", line 267, in train
    train_losses = self.train_epoch(train_loader)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\enhanced_trainer.py", line 152, in train_epoch
    step_losses = self.train_step(batch)
                  ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\enhanced_trainer.py", line 106, in train_step
    model_output = self.model(
                   ^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\models\enhanced_integrated_model.py", line 170, in forward
    layer_output = layer(hidden_states, attention_mask=attention_mask)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\transformers\models\gpt2\modeling_gpt2.py", line 404, in forward
    attn_outputs = self.attn(
                   ^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\transformers\models\gpt2\modeling_gpt2.py", line 335, in forward
    attn_output, attn_weights = attention_interface(
                                ^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\transformers\integrations\sdpa_attention.py", line 54, in sdpa_attention_forward
    attn_output = torch.nn.functional.scaled_dot_product_attention(
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: Expected attn_mask dtype to be bool or float or to match query dtype, but got attn_mask.dtype: __int64 and  query.dtype: float instead.

2025-05-29 13:51:37,107 - __main__ - INFO - Starting enhanced ThinkerModule training...
2025-05-29 13:51:37,107 - __main__ - INFO - Enhanced configuration created
2025-05-29 13:51:37,108 - __main__ - INFO - Training data validation passed: data/train.jsonl
2025-05-29 13:51:37,108 - __main__ - INFO - Initializing tokenizer...
2025-05-29 13:51:37,712 - __main__ - INFO - Initializing enhanced model...
2025-05-29 13:51:39,637 - __main__ - INFO - Enhanced model initialized with 328,636,311 parameters
2025-05-29 13:51:39,637 - __main__ - INFO -   Base LLM: 124,439,808
2025-05-29 13:51:39,637 - __main__ - INFO -   Enhanced ThinkerModule: 175,024,724
2025-05-29 13:51:39,637 - __main__ - INFO -   Enhanced Decision Mechanism: 12,637,507
2025-05-29 13:51:39,637 - __main__ - INFO -   Integration Components: 16,534,272
2025-05-29 13:51:39,637 - __main__ - INFO - Creating data loaders...
2025-05-29 13:51:39,640 - src.training.data_loader - INFO - Loaded 288 samples from data/train.jsonl
2025-05-29 13:51:39,640 - src.training.data_loader - INFO - Loaded 22 samples from data/eval.jsonl
2025-05-29 13:51:39,640 - __main__ - INFO - Training batches: 72
2025-05-29 13:51:39,640 - __main__ - INFO - Evaluation batches: 6
2025-05-29 13:51:39,640 - __main__ - INFO - Initializing enhanced trainer...
2025-05-29 13:51:40,329 - __main__ - INFO - Enhanced Training Configuration:
2025-05-29 13:51:40,330 - __main__ - INFO -   Batch size: 4
2025-05-29 13:51:40,330 - __main__ - INFO -   Gradient accumulation: 2
2025-05-29 13:51:40,330 - __main__ - INFO -   Learning rate: 5e-05
2025-05-29 13:51:40,330 - __main__ - INFO -   Number of epochs: 5
2025-05-29 13:51:40,330 - __main__ - INFO -   Enhanced loss weights:
2025-05-29 13:51:40,330 - __main__ - INFO -     LLM: 1.0
2025-05-29 13:51:40,330 - __main__ - INFO -     Thinker: 0.1
2025-05-29 13:51:40,330 - __main__ - INFO -     Decision: 0.15
2025-05-29 13:51:40,331 - __main__ - INFO -     Projection: 0.05
2025-05-29 13:51:40,331 - __main__ - INFO -   Progressive weighting: True
2025-05-29 13:51:40,331 - __main__ - INFO - Starting enhanced training...
2025-05-29 13:51:40,331 - src.training.enhanced_trainer - INFO - Starting enhanced training...
2025-05-29 13:52:04,917 - src.training.enhanced_trainer - INFO - Epoch 1/5
2025-05-29 13:52:04,918 - src.training.enhanced_trainer - INFO -   Train Loss: 515.0629
2025-05-29 13:52:04,918 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2976
2025-05-29 13:52:15,436 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 13:52:33,740 - src.training.enhanced_trainer - INFO - Epoch 2/5
2025-05-29 13:52:33,740 - src.training.enhanced_trainer - INFO -   Train Loss: 0.2854
2025-05-29 13:52:33,740 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2859
2025-05-29 13:52:44,545 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 13:53:02,840 - src.training.enhanced_trainer - INFO - Epoch 3/5
2025-05-29 13:53:02,840 - src.training.enhanced_trainer - INFO -   Train Loss: 0.2840
2025-05-29 13:53:02,840 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.3260
2025-05-29 13:53:20,741 - src.training.enhanced_trainer - INFO - Epoch 4/5
2025-05-29 13:53:20,741 - src.training.enhanced_trainer - INFO -   Train Loss: 0.2972
2025-05-29 13:53:20,741 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.3503
2025-05-29 13:53:38,921 - src.training.enhanced_trainer - INFO - Epoch 5/5
2025-05-29 13:53:38,921 - src.training.enhanced_trainer - INFO -   Train Loss: 0.3313
2025-05-29 13:53:38,921 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.3863
2025-05-29 13:53:38,921 - src.training.enhanced_trainer - INFO - Training completed!
2025-05-29 13:53:38,921 - __main__ - INFO - Enhanced training completed successfully!
2025-05-29 13:53:38,921 - __main__ - INFO - Final Training Statistics:
2025-05-29 13:53:38,921 - __main__ - INFO -   Final epoch: 4
2025-05-29 13:53:38,922 - __main__ - INFO -   Total steps: 180
2025-05-29 13:53:38,922 - __main__ - INFO -   Best loss: 0.2859
2025-05-29 13:53:38,922 - __main__ - INFO -   Training mode: joint
2025-05-29 13:53:50,062 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/final_enhanced_model.pt
2025-05-29 13:53:50,063 - __main__ - INFO - Final enhanced model saved as 'final_enhanced_model.pt'
2025-05-29 13:53:50,063 - __main__ - INFO - 
Testing reasoning-only training mode...
2025-05-29 13:53:50,064 - src.training.enhanced_trainer - INFO - Training mode: Reasoning components only
2025-05-29 13:53:50,064 - src.training.enhanced_trainer - INFO - Starting enhanced training...
2025-05-29 13:54:04,769 - src.training.enhanced_trainer - INFO - Epoch 1/1
2025-05-29 13:54:04,769 - src.training.enhanced_trainer - INFO -   Train Loss: 0.2044
2025-05-29 13:54:04,769 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2350
2025-05-29 13:54:15,395 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 13:54:15,396 - src.training.enhanced_trainer - INFO - Training completed!
2025-05-29 13:54:26,382 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/reasoning_only_enhanced_model.pt
2025-05-29 13:54:26,383 - __main__ - INFO - Reasoning-only model saved as 'reasoning_only_enhanced_model.pt'
2025-05-29 13:54:26,383 - __main__ - INFO - Enhanced training pipeline completed!
