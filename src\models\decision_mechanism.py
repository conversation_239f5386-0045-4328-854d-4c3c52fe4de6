"""
Decision Mechanism - Determines whether deeper reasoning is needed.
Analyzes input complexity and routes to appropriate processing path.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any, Tuple
import math


class ComplexityAnalyzer(nn.Module):
    """
    Analyzes input complexity to determine if ThinkerModule is needed.
    Uses multiple features to assess reasoning requirements.
    """

    def __init__(self, hidden_size: int, num_features: int = 4):
        super().__init__()
        self.hidden_size = hidden_size
        self.num_features = num_features

        # Feature extractors for different complexity aspects
        self.length_analyzer = nn.Linear(1, hidden_size // 4)
        self.semantic_analyzer = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.GELU(),
            nn.Linear(hidden_size // 2, hidden_size // 4)
        )
        self.pattern_analyzer = nn.Conv1d(
            in_channels=hidden_size,
            out_channels=hidden_size // 4,
            kernel_size=3,
            padding=1
        )
        self.attention_analyzer = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=4,
            dropout=0.1,
            batch_first=True
        )

        # Combine features
        self.feature_combiner = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.GELU(),
            nn.Linear(hidden_size // 2, num_features),
            nn.Tanh()
        )

    def forward(self, hidden_states: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Extract complexity features from input.

        Args:
            hidden_states: Input hidden states [batch_size, seq_len, hidden_size]
            attention_mask: Attention mask [batch_size, seq_len]

        Returns:
            complexity_features: Feature vector [batch_size, num_features]
        """
        batch_size, seq_len, hidden_size = hidden_states.shape

        # 1. Length-based complexity
        length_tensor = torch.tensor([[seq_len]], dtype=torch.float, device=hidden_states.device)
        length_tensor = length_tensor.expand(batch_size, 1)
        length_feature = self.length_analyzer(length_tensor)

        # 2. Semantic complexity (average pooling + analysis)
        if attention_mask is not None:
            mask_expanded = attention_mask.unsqueeze(-1).expand_as(hidden_states)
            masked_states = hidden_states * mask_expanded
            seq_lengths = attention_mask.sum(dim=1, keepdim=True).float()
            semantic_repr = masked_states.sum(dim=1) / seq_lengths
        else:
            semantic_repr = hidden_states.mean(dim=1)

        semantic_feature = self.semantic_analyzer(semantic_repr)

        # 3. Pattern complexity (convolutional analysis)
        # Transpose for conv1d: [batch_size, hidden_size, seq_len]
        conv_input = hidden_states.transpose(1, 2)
        pattern_feature = self.pattern_analyzer(conv_input)
        pattern_feature = F.adaptive_avg_pool1d(pattern_feature, 1).squeeze(-1)

        # 4. Attention complexity (self-attention variance)
        key_padding_mask = None
        if attention_mask is not None:
            # Convert attention mask to key padding mask (True for padding positions)
            key_padding_mask = (attention_mask == 0)

        attn_output, attn_weights = self.attention_analyzer(
            hidden_states, hidden_states, hidden_states,
            key_padding_mask=key_padding_mask
        )
        # Use attention weight variance as complexity indicator
        # attn_weights shape: [batch_size, num_heads, seq_len, seq_len]
        # Calculate variance across the last dimension (attention targets)
        attn_variance = attn_weights.var(dim=-1)  # [batch_size, num_heads, seq_len]
        # Average across sequence length and heads
        attn_complexity = attn_variance.mean(dim=-1).mean(dim=-1, keepdim=True)  # [batch_size, 1]
        attn_feature = attn_complexity.expand(batch_size, hidden_size // 4)

        # Combine all features
        combined_features = torch.cat([
            length_feature,
            semantic_feature,
            pattern_feature,
            attn_feature
        ], dim=-1)

        complexity_features = self.feature_combiner(combined_features)
        return complexity_features


class DecisionNetwork(nn.Module):
    """
    Neural network that makes the final decision based on complexity features.
    """

    def __init__(self, input_size: int, hidden_size: int, num_layers: int = 2):
        super().__init__()

        layers = []
        current_size = input_size

        for i in range(num_layers):
            layers.extend([
                nn.Linear(current_size, hidden_size),
                nn.GELU(),
                nn.Dropout(0.1)
            ])
            current_size = hidden_size

        # Final decision layer
        layers.append(nn.Linear(current_size, 1))

        self.network = nn.Sequential(*layers)

    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """
        Make decision based on features.

        Args:
            features: Complexity features [batch_size, num_features]

        Returns:
            decision_logits: Raw decision scores [batch_size, 1]
        """
        return self.network(features)


class DecisionMechanism(nn.Module):
    """
    Decision mechanism that determines whether to use ThinkerModule.

    This component:
    1. Analyzes input complexity using multiple features
    2. Makes a binary decision: use ThinkerModule or go directly to LLM
    3. Provides confidence scores and explanations
    4. Can be trained with supervision or learned through reinforcement
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config

        self.hidden_size = config['hidden_size']
        self.num_layers = config['num_layers']
        self.threshold = config['threshold']
        self.num_complexity_features = config['complexity_features']

        # Input projection (from LLM hidden size to decision hidden size)
        self.input_projection = nn.Linear(
            config.get('input_hidden_size', 768),  # Default LLM hidden size
            self.hidden_size
        )

        # Complexity analyzer
        self.complexity_analyzer = ComplexityAnalyzer(
            hidden_size=self.hidden_size,
            num_features=self.num_complexity_features
        )

        # Decision network
        self.decision_network = DecisionNetwork(
            input_size=self.num_complexity_features,
            hidden_size=self.hidden_size,
            num_layers=self.num_layers
        )

        # Confidence estimator
        self.confidence_estimator = nn.Sequential(
            nn.Linear(self.num_complexity_features, self.hidden_size // 2),
            nn.GELU(),
            nn.Linear(self.hidden_size // 2, 1),
            nn.Sigmoid()
        )

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)

    def forward(self, input_hidden_states: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                return_features: bool = False) -> Dict[str, torch.Tensor]:
        """
        Make decision about whether to use ThinkerModule.

        Args:
            input_hidden_states: Hidden states from input processing [batch_size, seq_len, input_hidden_size]
            attention_mask: Attention mask [batch_size, seq_len]
            return_features: Whether to return complexity features

        Returns:
            Dictionary containing:
            - decision: Binary decision (0: direct LLM, 1: use ThinkerModule) [batch_size]
            - decision_logits: Raw decision scores [batch_size, 1]
            - confidence: Confidence in decision [batch_size, 1]
            - complexity_features: Complexity features (if return_features=True)
        """
        # Project input to decision hidden size
        projected_states = self.input_projection(input_hidden_states)

        # Analyze complexity
        complexity_features = self.complexity_analyzer(projected_states, attention_mask)

        # Make decision
        decision_logits = self.decision_network(complexity_features)
        decision_probs = torch.sigmoid(decision_logits)

        # Binary decision based on threshold
        decisions = (decision_probs > self.threshold).float().squeeze(-1)

        # Estimate confidence
        confidence = self.confidence_estimator(complexity_features)

        result = {
            'decision': decisions,
            'decision_logits': decision_logits,
            'decision_probs': decision_probs,
            'confidence': confidence
        }

        if return_features:
            result['complexity_features'] = complexity_features

        return result

    def set_threshold(self, new_threshold: float):
        """Update decision threshold."""
        self.threshold = new_threshold

    def get_decision_explanation(self, complexity_features: torch.Tensor) -> Dict[str, float]:
        """
        Provide explanation for decision based on complexity features.

        Args:
            complexity_features: Complexity features [batch_size, num_features]

        Returns:
            Dictionary with feature explanations
        """
        # Assuming features are [length, semantic, pattern, attention]
        features = complexity_features[0].cpu().numpy()  # Take first batch item

        feature_names = ['length_complexity', 'semantic_complexity',
                        'pattern_complexity', 'attention_complexity']

        explanation = {}
        for i, name in enumerate(feature_names):
            if i < len(features):
                explanation[name] = float(features[i])

        return explanation

    def compute_decision_loss(self, decision_logits: torch.Tensor,
                             target_decisions: torch.Tensor,
                             confidence: torch.Tensor) -> torch.Tensor:
        """
        Compute loss for training the decision mechanism.

        Args:
            decision_logits: Raw decision scores [batch_size, 1]
            target_decisions: Target decisions [batch_size]
            confidence: Confidence scores [batch_size, 1]

        Returns:
            Combined loss including decision accuracy and confidence calibration
        """
        # Binary cross-entropy loss for decisions
        decision_loss = F.binary_cross_entropy_with_logits(
            decision_logits.squeeze(-1), target_decisions
        )

        # Confidence calibration loss
        decision_probs = torch.sigmoid(decision_logits.squeeze(-1))
        correct_predictions = (decision_probs > 0.5) == (target_decisions > 0.5)
        confidence_targets = correct_predictions.float()

        confidence_loss = F.mse_loss(confidence.squeeze(-1), confidence_targets)

        # Combined loss
        total_loss = decision_loss + 0.1 * confidence_loss

        return total_loss
