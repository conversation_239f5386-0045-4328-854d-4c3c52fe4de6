"""
Enhanced ThinkerModule with sophisticated reasoning capabilities.
This implements the improved architecture with attention mechanisms,
positional encoding, and quality assessment.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
import math


class PositionalEncoding(nn.Module):
    """Positional encoding for reasoning steps."""

    def __init__(self, d_model: int, max_len: int = 50):
        super().__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe.unsqueeze(0))

    def forward(self, x):
        return x + self.pe[:, :x.size(1)]


class ReasoningAttention(nn.Module):
    """Multi-head attention for reasoning steps."""

    def __init__(self, d_model: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads

        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        self.out_linear = nn.Linear(d_model, d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)

        # Linear transformations
        Q = self.q_linear(query).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.k_linear(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.v_linear(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)

        # Attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)

        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        context = torch.matmul(attention_weights, V)
        context = context.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model)

        return self.out_linear(context)


class ReasoningStep(nn.Module):
    """Single reasoning step with attention and feed-forward."""

    def __init__(self, d_model: int, num_heads: int = 8, d_ff: int = 2048, dropout: float = 0.1):
        super().__init__()
        self.self_attention = ReasoningAttention(d_model, num_heads, dropout)
        self.cross_attention = ReasoningAttention(d_model, num_heads, dropout)
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model)
        )
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x, context, step_mask=None, context_mask=None):
        # Self-attention on reasoning steps
        attn_output = self.self_attention(x, x, x, step_mask)
        x = self.norm1(x + self.dropout(attn_output))

        # Cross-attention with input context
        if context is not None:
            cross_attn_output = self.cross_attention(x, context, context, context_mask)
            x = self.norm2(x + self.dropout(cross_attn_output))

        # Feed-forward
        ff_output = self.feed_forward(x)
        x = self.norm3(x + self.dropout(ff_output))

        return x


class ComplexityAnalyzer(nn.Module):
    """Analyzes input complexity to determine reasoning depth."""

    def __init__(self, d_model: int, vocab_size: int):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, d_model)
        self.encoder = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(d_model, nhead=8, batch_first=True),
            num_layers=2
        )
        self.complexity_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1),
            nn.Sigmoid()
        )

    def forward(self, input_ids, attention_mask=None):
        # Embed and encode input
        x = self.embedding(input_ids)

        # Handle attention mask for transformer encoder
        if attention_mask is not None:
            # Convert to boolean mask for transformer (True = not masked, False = masked)
            src_key_padding_mask = ~attention_mask.bool()
        else:
            src_key_padding_mask = None

        x = self.encoder(x, src_key_padding_mask=src_key_padding_mask)

        # Pool and predict complexity
        if attention_mask is not None:
            # Masked average pooling
            mask_expanded = attention_mask.unsqueeze(-1).expand_as(x).float()
            x_masked = x * mask_expanded
            pooled = x_masked.sum(dim=1) / attention_mask.sum(dim=1, keepdim=True).float()
        else:
            pooled = x.mean(dim=1)

        complexity_score = self.complexity_head(pooled)
        return complexity_score


class EnhancedThinkerModule(nn.Module):
    """Enhanced ThinkerModule with sophisticated reasoning capabilities."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.d_model = config.get('d_model', 768)
        self.vocab_size = config.get('vocab_size', 50257)
        self.max_reasoning_steps = config.get('max_reasoning_steps', 16)
        self.num_reasoning_layers = config.get('num_reasoning_layers', 6)

        # Core components
        self.embedding = nn.Embedding(self.vocab_size, self.d_model)
        self.positional_encoding = PositionalEncoding(self.d_model, self.max_reasoning_steps)

        # Reasoning layers
        self.reasoning_layers = nn.ModuleList([
            ReasoningStep(self.d_model) for _ in range(self.num_reasoning_layers)
        ])

        # Output projection
        self.output_projection = nn.Linear(self.d_model, self.vocab_size)

        # Stopping mechanism
        self.stop_predictor = nn.Sequential(
            nn.Linear(self.d_model, self.d_model // 2),
            nn.ReLU(),
            nn.Linear(self.d_model // 2, 1),
            nn.Sigmoid()
        )

        # Complexity analyzer
        self.complexity_analyzer = ComplexityAnalyzer(self.d_model, self.vocab_size)

        # Quality assessor
        self.quality_assessor = nn.Sequential(
            nn.Linear(self.d_model, self.d_model // 2),
            nn.ReLU(),
            nn.Linear(self.d_model // 2, 1),
            nn.Sigmoid()
        )

        # Temperature for reasoning generation
        self.reasoning_temperature = 1.0

    def analyze_complexity(self, input_ids, attention_mask=None):
        """Analyze input complexity to determine reasoning approach."""
        complexity_score = self.complexity_analyzer(input_ids, attention_mask)

        # Determine number of reasoning steps based on complexity
        min_steps = 2
        max_steps = self.max_reasoning_steps
        num_steps = min_steps + int((max_steps - min_steps) * complexity_score.mean().item())

        return {
            'complexity_score': complexity_score,
            'suggested_steps': num_steps,
            'use_reasoning': complexity_score.mean() > 0.3  # Threshold for using reasoning
        }

    def generate_reasoning_step(self, step_idx, context_embedding, previous_steps=None):
        """Generate a single reasoning step."""
        batch_size = context_embedding.size(0)
        device = context_embedding.device

        if previous_steps is None:
            # Initialize first step with learnable start token
            step_embedding = torch.randn(batch_size, 1, self.d_model, device=device) * 0.1
        else:
            # Use previous steps as context
            step_embedding = previous_steps

        # Add positional encoding
        step_embedding = self.positional_encoding(step_embedding)

        # Apply reasoning layers
        for layer in self.reasoning_layers:
            step_embedding = layer(step_embedding, context_embedding)

        # Generate step tokens
        step_logits = self.output_projection(step_embedding[:, -1:])  # Last step only

        # Apply temperature
        step_logits = step_logits / self.reasoning_temperature

        # Predict if we should stop
        stop_prob = self.stop_predictor(step_embedding[:, -1])

        # Assess quality of current reasoning
        quality_score = self.quality_assessor(step_embedding[:, -1])

        return {
            'step_logits': step_logits,
            'step_embedding': step_embedding,
            'stop_probability': stop_prob,
            'quality_score': quality_score
        }

    def forward(self, input_ids, attention_mask=None, max_new_steps=None):
        """Forward pass with dynamic reasoning generation."""
        batch_size = input_ids.size(0)
        device = input_ids.device

        # Analyze complexity
        complexity_info = self.analyze_complexity(input_ids, attention_mask)

        if not complexity_info['use_reasoning']:
            # Skip reasoning for simple inputs
            return {
                'reasoning_steps': [],
                'stop_probabilities': [],
                'quality_scores': [],
                'complexity_info': complexity_info,
                'used_reasoning': False
            }

        # Embed input context
        context_embedding = self.embedding(input_ids)

        # Generate reasoning steps
        reasoning_steps = []
        stop_probabilities = []
        quality_scores = []
        current_steps = None

        max_steps = max_new_steps or complexity_info['suggested_steps']

        for step_idx in range(max_steps):
            step_output = self.generate_reasoning_step(
                step_idx, context_embedding, current_steps
            )

            reasoning_steps.append(step_output['step_logits'])
            stop_probabilities.append(step_output['stop_probability'])
            quality_scores.append(step_output['quality_score'])

            # Update current steps
            if current_steps is None:
                current_steps = step_output['step_embedding']
            else:
                current_steps = torch.cat([current_steps, step_output['step_embedding'][:, -1:]], dim=1)

            # Check stopping condition
            if step_output['stop_probability'].mean() > 0.7:  # Stop threshold
                break

            # Quality-based early stopping
            if step_output['quality_score'].mean() < 0.3:  # Quality threshold
                break

        return {
            'reasoning_steps': reasoning_steps,
            'stop_probabilities': stop_probabilities,
            'quality_scores': quality_scores,
            'complexity_info': complexity_info,
            'used_reasoning': True,
            'final_reasoning_embedding': current_steps
        }

    def generate_reasoning_text(self, reasoning_steps, tokenizer, max_tokens_per_step=20, stop_probabilities=None):
        """Convert reasoning step logits to text."""
        reasoning_texts = []

        for i, step_logits in enumerate(reasoning_steps):
            # Sample from logits
            probs = F.softmax(step_logits / self.reasoning_temperature, dim=-1)

            # Generate tokens for this step
            step_tokens = []
            current_logits = step_logits[0, 0]  # First batch, first position

            for _ in range(max_tokens_per_step):
                # Sample next token
                token_probs = F.softmax(current_logits / self.reasoning_temperature, dim=-1)
                next_token = torch.multinomial(token_probs, 1)
                step_tokens.append(next_token.item())

                # Simple stopping condition
                if next_token.item() == tokenizer.eos_token_id:
                    break

            # Decode tokens to text
            step_text = tokenizer.decode(step_tokens, skip_special_tokens=True)
            reasoning_texts.append(f"Step {i+1}: {step_text}")

        return reasoning_texts
