"""
Demo script for the enhanced ThinkerModule architecture.
This tests the improved reasoning capabilities and identifies issues.
"""

import os
import sys
import torch
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from transformers import AutoTokenizer
from src.models.enhanced_integrated_model import EnhancedIntegratedLL<PERSON><PERSON><PERSON><PERSON>hinker


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def create_demo_config():
    """Create configuration for demo."""
    return {
        'model': {
            'thinker': {
                'max_reasoning_steps': 12,
                'num_reasoning_layers': 4,
                'd_model': 768,
                'vocab_size': 50257
            },
            'num_integration_layers': 3
        }
    }


def load_model(checkpoint_path: str, config: dict, logger):
    """Load model from checkpoint."""
    model = EnhancedIntegratedLLM<PERSON>ithThinker(config)

    if os.path.exists(checkpoint_path):
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"Enhanced model loaded from {checkpoint_path}")
        logger.info(f"Training epoch: {checkpoint.get('epoch', 'unknown')}")
        logger.info(f"Training step: {checkpoint.get('global_step', 'unknown')}")
        logger.info(f"Best loss: {checkpoint.get('best_loss', 'unknown')}")
    else:
        logger.warning(f"Checkpoint not found: {checkpoint_path}")
        logger.warning("Using randomly initialized enhanced model")

    return model


def test_reasoning_capabilities(model, tokenizer, prompts, logger):
    """Test the enhanced reasoning capabilities."""
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    logger.info("Testing enhanced reasoning capabilities...")
    
    for i, prompt in enumerate(prompts):
        logger.info(f"\n{'='*60}")
        logger.info(f"Test {i+1}: {prompt}")
        logger.info(f"{'='*60}")
        
        # Tokenize input
        input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)
        
        # Test with forced reasoning
        logger.info("\n🧠 Enhanced reasoning mode:")
        with torch.no_grad():
            result = model.generate(
                input_ids=input_ids,
                max_new_tokens=50,
                temperature=0.8,
                top_p=0.9,
                force_thinking=True,
                return_reasoning=True,
                tokenizer=tokenizer
            )
        
        # Decode generated text
        generated_text = tokenizer.decode(
            result['generated_ids'][0],
            skip_special_tokens=True
        )
        
        logger.info(f"Generated: {generated_text[len(prompt):]}")
        logger.info(f"Used thinking: {result['thinking_used'][0].item()}")
        
        # Analyze reasoning if available
        if 'reasoning_info' in result and result['reasoning_info']:
            reasoning_info = result['reasoning_info']
            
            if reasoning_info.get('used_reasoning', False):
                logger.info(f"Complexity score: {reasoning_info['complexity_info']['complexity_score'].item():.3f}")
                logger.info(f"Suggested steps: {reasoning_info['complexity_info']['suggested_steps']}")
                
                # Show reasoning steps
                reasoning_steps = reasoning_info.get('reasoning_steps', [])
                quality_scores = reasoning_info.get('quality_scores', [])
                stop_probs = reasoning_info.get('stop_probabilities', [])
                
                logger.info(f"Generated {len(reasoning_steps)} reasoning steps:")
                
                for j, step_logits in enumerate(reasoning_steps):
                    # Sample from step logits to show reasoning
                    step_probs = torch.softmax(step_logits[0, 0] / 0.8, dim=-1)
                    top_tokens = torch.topk(step_probs, k=5)
                    
                    step_tokens = [tokenizer.decode([token_id]) for token_id in top_tokens.indices]
                    step_probs_list = [f"{prob:.3f}" for prob in top_tokens.values]
                    
                    quality = quality_scores[j].item() if j < len(quality_scores) else 0.0
                    stop_prob = stop_probs[j].item() if j < len(stop_probs) else 0.0
                    
                    logger.info(f"  Step {j+1}: {step_tokens} (quality: {quality:.3f}, stop: {stop_prob:.3f})")
            else:
                logger.info("Reasoning was skipped (low complexity)")
        
        # Test with automatic decision
        logger.info("\n🤖 Automatic decision mode:")
        with torch.no_grad():
            result = model.generate(
                input_ids=input_ids,
                max_new_tokens=50,
                temperature=0.8,
                top_p=0.9,
                force_thinking=None,
                return_reasoning=False,
                tokenizer=tokenizer
            )
        
        generated_text = tokenizer.decode(
            result['generated_ids'][0],
            skip_special_tokens=True
        )
        
        decision_info = result['decision_info']
        decision_prob = decision_info['decision_probs'][0].item()
        confidence = decision_info['confidence'][0].item()
        
        logger.info(f"Generated: {generated_text[len(prompt):]}")
        logger.info(f"Used thinking: {result['thinking_used'][0].item()}")
        logger.info(f"Decision probability: {decision_prob:.3f}")
        logger.info(f"Confidence: {confidence:.3f}")


def test_decision_mechanism(model, tokenizer, test_cases, logger):
    """Test the enhanced decision mechanism."""
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    logger.info("\n" + "="*60)
    logger.info("TESTING ENHANCED DECISION MECHANISM")
    logger.info("="*60)
    
    decision_stats = {'thinking_used': 0, 'direct_used': 0}
    
    for case in test_cases:
        prompt = case['prompt']
        expected_complexity = case['expected_complexity']
        
        logger.info(f"\nPrompt: {prompt}")
        logger.info(f"Expected complexity: {expected_complexity}")
        
        # Tokenize input
        input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)
        
        # Test decision mechanism
        with torch.no_grad():
            # Get input embeddings
            input_embeddings = model.base_llm.get_input_embeddings()(input_ids)
            
            # Make decision
            decision_output = model.decision_mechanism(
                input_ids, None, input_embeddings, tokenizer
            )
            
            decision_prob = decision_output['decision_probs'][0].item()
            confidence = decision_output['confidence'][0].item()
            
            # Also test complexity analysis
            complexity_info = model.thinker.analyze_complexity(input_ids)
            complexity_score = complexity_info['complexity_score'][0].item()
            
            thinking_decision = decision_prob > 0.5
            
            if thinking_decision:
                decision_stats['thinking_used'] += 1
            else:
                decision_stats['direct_used'] += 1
            
            logger.info(f"  Decision: {'Thinking' if thinking_decision else 'Direct'}")
            logger.info(f"  Decision probability: {decision_prob:.3f}")
            logger.info(f"  Confidence: {confidence:.3f}")
            logger.info(f"  Complexity score: {complexity_score:.3f}")
            logger.info(f"  Suggested steps: {complexity_info['suggested_steps']}")
            
            # Check if decision aligns with expected complexity
            correct_decision = (thinking_decision and expected_complexity == 'high') or \
                             (not thinking_decision and expected_complexity == 'low')
            logger.info(f"  Decision correctness: {'✓' if correct_decision else '✗'}")
    
    total_cases = len(test_cases)
    thinking_ratio = decision_stats['thinking_used'] / total_cases
    
    logger.info(f"\nDecision Mechanism Statistics:")
    logger.info(f"  Thinking used: {decision_stats['thinking_used']}/{total_cases} ({thinking_ratio:.1%})")
    logger.info(f"  Direct used: {decision_stats['direct_used']}/{total_cases} ({1-thinking_ratio:.1%})")
    
    return decision_stats


def main():
    logger = setup_logging()
    logger.info("Starting enhanced ThinkerModule demo...")

    # Create configuration
    config = create_demo_config()

    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token

    # Test prompts with varying complexity
    test_prompts = [
        "The capital of France is",
        "To solve 2x + 5 = 13, we need to",
        "What is the weather like today?",
        "If a car travels 60 miles per hour for 3 hours, how far does it travel?",
        "Explain the concept of photosynthesis in detail",
    ]

    # Decision mechanism test cases
    decision_test_cases = [
        {'prompt': "Hello", 'expected_complexity': 'low'},
        {'prompt': "What is 2 + 2?", 'expected_complexity': 'low'},
        {'prompt': "The capital of France is", 'expected_complexity': 'low'},
        {'prompt': "If a sequence is 100, 50, 25, 12.5, what is the next number?", 'expected_complexity': 'high'},
        {'prompt': "Explain the theory of relativity and its implications", 'expected_complexity': 'high'},
        {'prompt': "If a train leaves station A at 2 PM traveling at 60 mph and another train leaves station B at 3 PM traveling at 80 mph, when will they meet?", 'expected_complexity': 'high'},
    ]

    # Test different model versions
    model_checkpoints = [
        ('checkpoints/final_enhanced_model.pt', 'Enhanced Model'),
        ('checkpoints/reasoning_only_enhanced_model.pt', 'Reasoning-Only Enhanced Model')
    ]

    for checkpoint_path, model_name in model_checkpoints:
        if not os.path.exists(checkpoint_path):
            logger.warning(f"Checkpoint not found: {checkpoint_path}")
            continue
            
        logger.info(f"\n{'='*80}")
        logger.info(f"TESTING {model_name.upper()}")
        logger.info(f"{'='*80}")

        # Load model
        model = load_model(checkpoint_path, config, logger)
        
        # Show model info
        model_info = model.get_model_info()
        logger.info(f"Model parameters: {model_info['total_params']:,}")
        
        # Test reasoning capabilities
        test_reasoning_capabilities(model, tokenizer, test_prompts, logger)
        
        # Test decision mechanism
        test_decision_mechanism(model, tokenizer, decision_test_cases, logger)

    logger.info("\n" + "="*80)
    logger.info("ENHANCED DEMO COMPLETED")
    logger.info("="*80)
    logger.info("\nKey improvements to look for:")
    logger.info("1. More coherent reasoning steps")
    logger.info("2. Better decision making based on complexity")
    logger.info("3. Quality-aware reasoning generation")
    logger.info("4. Appropriate stopping behavior")
    logger.info("5. Integration of reasoning with text generation")


if __name__ == '__main__':
    main()
