2025-05-29 11:23:36,837 - __main__ - INFO - Starting improved model retraining...
2025-05-29 11:23:36,837 - __main__ - INFO - Loading configuration from config/model_config.yaml
2025-05-29 11:23:36,841 - __main__ - INFO - Training data validation passed: data/train.jsonl
2025-05-29 11:23:36,841 - __main__ - INFO - Initializing tokenizer...
2025-05-29 11:23:37,305 - __main__ - INFO - Initializing model...
2025-05-29 11:23:40,981 - __main__ - INFO - Model initialized with 284,039,512 parameters
2025-05-29 11:23:40,982 - __main__ - INFO -   LLM: 163,795,968
2025-05-29 11:23:40,982 - __main__ - INFO -   ThinkerModule: 75,678,802
2025-05-29 11:23:40,982 - __main__ - INFO -   Decision Mechanism: 652,038
2025-05-29 11:23:40,982 - __main__ - INFO - Creating data loaders...
2025-05-29 11:23:40,984 - src.training.data_loader - INFO - Loaded 288 samples from data/train.jsonl
2025-05-29 11:23:40,985 - src.training.data_loader - INFO - Loaded 22 samples from data/eval.jsonl
2025-05-29 11:23:40,985 - __main__ - INFO - Training batches: 144
2025-05-29 11:23:40,985 - __main__ - INFO - Evaluation batches: 11
2025-05-29 11:23:40,985 - __main__ - INFO - Initializing trainer...
2025-05-29 11:23:41,365 - __main__ - INFO - Training Configuration:
2025-05-29 11:23:41,365 - __main__ - INFO -   Batch size: 2
2025-05-29 11:23:41,365 - __main__ - INFO -   Learning rate: 5e-05
2025-05-29 11:23:41,365 - __main__ - INFO -   Number of epochs: 3
2025-05-29 11:23:41,365 - __main__ - INFO -   Loss weights:
2025-05-29 11:23:41,365 - __main__ - INFO -     LLM: 1.0
2025-05-29 11:23:41,365 - __main__ - INFO -     Thinker: 0.1
2025-05-29 11:23:41,365 - __main__ - INFO -     Decision: 0.2
2025-05-29 11:23:41,365 - __main__ - INFO -     Projection: 0.05
2025-05-29 11:23:41,365 - __main__ - INFO - Starting training...
2025-05-29 11:23:41,366 - src.training.trainer - INFO - Starting training for 3 epochs
2025-05-29 11:23:41,366 - src.training.trainer - INFO - Training mode: joint
2025-05-29 11:23:41,367 - src.training.trainer - INFO - Model parameters: 284,039,512
2025-05-29 11:23:41,839 - __main__ - ERROR - Training failed with error: 'list' object has no attribute 'device'
2025-05-29 11:23:41,841 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\retrain_improved_model.py", line 142, in main
    history = trainer.train(train_loader, eval_loader)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\trainer.py", line 331, in train
    train_metrics = self.train_epoch(train_loader, epoch)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\trainer.py", line 172, in train_epoch
    losses = self.loss_fn(
             ^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\loss_functions.py", line 334, in forward
    thinker_loss = self.thinker_loss(reasoning_output, target_reasoning)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\loss_functions.py", line 123, in forward
    if target_step.device != device:
       ^^^^^^^^^^^^^^^^^^
AttributeError: 'list' object has no attribute 'device'

2025-05-29 11:24:17,511 - __main__ - INFO - Starting improved model retraining...
2025-05-29 11:24:17,511 - __main__ - INFO - Loading configuration from config/model_config.yaml
2025-05-29 11:24:17,517 - __main__ - INFO - Training data validation passed: data/train.jsonl
2025-05-29 11:24:17,517 - __main__ - INFO - Initializing tokenizer...
2025-05-29 11:24:17,974 - __main__ - INFO - Initializing model...
2025-05-29 11:24:21,683 - __main__ - INFO - Model initialized with 284,039,512 parameters
2025-05-29 11:24:21,684 - __main__ - INFO -   LLM: 163,795,968
2025-05-29 11:24:21,684 - __main__ - INFO -   ThinkerModule: 75,678,802
2025-05-29 11:24:21,684 - __main__ - INFO -   Decision Mechanism: 652,038
2025-05-29 11:24:21,684 - __main__ - INFO - Creating data loaders...
2025-05-29 11:24:21,686 - src.training.data_loader - INFO - Loaded 288 samples from data/train.jsonl
2025-05-29 11:24:21,686 - src.training.data_loader - INFO - Loaded 22 samples from data/eval.jsonl
2025-05-29 11:24:21,687 - __main__ - INFO - Training batches: 144
2025-05-29 11:24:21,687 - __main__ - INFO - Evaluation batches: 11
2025-05-29 11:24:21,687 - __main__ - INFO - Initializing trainer...
2025-05-29 11:24:22,092 - __main__ - INFO - Training Configuration:
2025-05-29 11:24:22,092 - __main__ - INFO -   Batch size: 2
2025-05-29 11:24:22,092 - __main__ - INFO -   Learning rate: 5e-05
2025-05-29 11:24:22,092 - __main__ - INFO -   Number of epochs: 3
2025-05-29 11:24:22,092 - __main__ - INFO -   Loss weights:
2025-05-29 11:24:22,092 - __main__ - INFO -     LLM: 1.0
2025-05-29 11:24:22,092 - __main__ - INFO -     Thinker: 0.1
2025-05-29 11:24:22,092 - __main__ - INFO -     Decision: 0.2
2025-05-29 11:24:22,092 - __main__ - INFO -     Projection: 0.05
2025-05-29 11:24:22,092 - __main__ - INFO - Starting training...
2025-05-29 11:24:22,093 - src.training.trainer - INFO - Starting training for 3 epochs
2025-05-29 11:24:22,093 - src.training.trainer - INFO - Training mode: joint
2025-05-29 11:24:22,093 - src.training.trainer - INFO - Model parameters: 284,039,512
2025-05-29 11:24:27,634 - __main__ - ERROR - Training failed with error: Expected input batch_size (1) to match target batch_size (9).
2025-05-29 11:24:27,637 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\retrain_improved_model.py", line 142, in main
    history = trainer.train(train_loader, eval_loader)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\trainer.py", line 331, in train
    train_metrics = self.train_epoch(train_loader, epoch)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\trainer.py", line 172, in train_epoch
    losses = self.loss_fn(
             ^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\loss_functions.py", line 370, in forward
    thinker_loss = self.thinker_loss(reasoning_output, target_reasoning)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\vlinhd11\Projects\LCMS_LLM\MOE-LLM\geneticAI\v3\src\training\loss_functions.py", line 138, in forward
    loss = self.loss_fn(
           ^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\modules\loss.py", line 1297, in forward
    return F.cross_entropy(
           ^^^^^^^^^^^^^^^^
  File "D:\.conda\envs\tts\Lib\site-packages\torch\nn\functional.py", line 3494, in cross_entropy
    return torch._C._nn.cross_entropy_loss(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ValueError: Expected input batch_size (1) to match target batch_size (9).

2025-05-29 11:25:00,840 - __main__ - INFO - Starting improved model retraining...
2025-05-29 11:25:00,840 - __main__ - INFO - Loading configuration from config/model_config.yaml
2025-05-29 11:25:00,844 - __main__ - INFO - Training data validation passed: data/train.jsonl
2025-05-29 11:25:00,844 - __main__ - INFO - Initializing tokenizer...
2025-05-29 11:25:01,360 - __main__ - INFO - Initializing model...
2025-05-29 11:25:05,037 - __main__ - INFO - Model initialized with 284,039,512 parameters
2025-05-29 11:25:05,037 - __main__ - INFO -   LLM: 163,795,968
2025-05-29 11:25:05,037 - __main__ - INFO -   ThinkerModule: 75,678,802
2025-05-29 11:25:05,038 - __main__ - INFO -   Decision Mechanism: 652,038
2025-05-29 11:25:05,038 - __main__ - INFO - Creating data loaders...
2025-05-29 11:25:05,039 - src.training.data_loader - INFO - Loaded 288 samples from data/train.jsonl
2025-05-29 11:25:05,039 - src.training.data_loader - INFO - Loaded 22 samples from data/eval.jsonl
2025-05-29 11:25:05,039 - __main__ - INFO - Training batches: 144
2025-05-29 11:25:05,039 - __main__ - INFO - Evaluation batches: 11
2025-05-29 11:25:05,039 - __main__ - INFO - Initializing trainer...
2025-05-29 11:25:05,436 - __main__ - INFO - Training Configuration:
2025-05-29 11:25:05,436 - __main__ - INFO -   Batch size: 2
2025-05-29 11:25:05,436 - __main__ - INFO -   Learning rate: 5e-05
2025-05-29 11:25:05,436 - __main__ - INFO -   Number of epochs: 3
2025-05-29 11:25:05,436 - __main__ - INFO -   Loss weights:
2025-05-29 11:25:05,436 - __main__ - INFO -     LLM: 1.0
2025-05-29 11:25:05,436 - __main__ - INFO -     Thinker: 0.1
2025-05-29 11:25:05,436 - __main__ - INFO -     Decision: 0.2
2025-05-29 11:25:05,436 - __main__ - INFO -     Projection: 0.05
2025-05-29 11:25:05,436 - __main__ - INFO - Starting training...
2025-05-29 11:25:05,437 - src.training.trainer - INFO - Starting training for 3 epochs
2025-05-29 11:25:05,437 - src.training.trainer - INFO - Training mode: joint
2025-05-29 11:25:05,437 - src.training.trainer - INFO - Model parameters: 284,039,512
2025-05-29 11:25:51,170 - src.training.trainer - INFO - Step 100: Total Loss = 4.4867
2025-05-29 11:25:51,170 - src.training.trainer - INFO -   LLM Loss: 1.2925
2025-05-29 11:25:51,170 - src.training.trainer - INFO -   Thinker Loss: 30.5061
2025-05-29 11:25:51,170 - src.training.trainer - INFO -   Decision Loss: 0.7182
2025-05-29 11:25:51,171 - src.training.trainer - INFO -   Projection Loss: 0.0000
2025-05-29 11:26:10,335 - src.training.trainer - INFO - Epoch 0: Train Loss = 4.0136
2025-05-29 11:26:11,753 - src.training.trainer - INFO - Epoch 0: Eval Loss = 2.3779
2025-05-29 11:26:22,759 - src.training.trainer - INFO - Checkpoint saved: checkpoints\best_model.pt
2025-05-29 11:26:22,759 - src.training.trainer - INFO - New best model saved with loss: 2.3779
2025-05-29 11:26:47,647 - src.training.trainer - INFO - Step 200: Total Loss = 1.8132
2025-05-29 11:26:47,647 - src.training.trainer - INFO -   LLM Loss: 0.1775
2025-05-29 11:26:47,647 - src.training.trainer - INFO -   Thinker Loss: 14.9205
2025-05-29 11:26:47,647 - src.training.trainer - INFO -   Decision Loss: 0.7182
2025-05-29 11:26:47,647 - src.training.trainer - INFO -   Projection Loss: 0.0000
2025-05-29 11:27:27,676 - src.training.trainer - INFO - Epoch 1: Train Loss = 0.9602
2025-05-29 11:27:33,103 - src.training.trainer - INFO - Step 300: Total Loss = 0.2837
2025-05-29 11:27:33,103 - src.training.trainer - INFO -   LLM Loss: 0.1128
2025-05-29 11:27:33,103 - src.training.trainer - INFO -   Thinker Loss: 0.2724
2025-05-29 11:27:33,103 - src.training.trainer - INFO -   Decision Loss: 0.7180
2025-05-29 11:27:33,103 - src.training.trainer - INFO -   Projection Loss: 0.0000
2025-05-29 11:28:19,408 - src.training.trainer - INFO - Step 400: Total Loss = 0.2560
2025-05-29 11:28:19,408 - src.training.trainer - INFO -   LLM Loss: 0.0949
2025-05-29 11:28:19,408 - src.training.trainer - INFO -   Thinker Loss: 0.1751
2025-05-29 11:28:19,408 - src.training.trainer - INFO -   Decision Loss: 0.7182
2025-05-29 11:28:19,408 - src.training.trainer - INFO -   Projection Loss: 0.0000
2025-05-29 11:28:31,697 - src.training.trainer - INFO - Epoch 2: Train Loss = 0.2599
2025-05-29 11:28:31,697 - src.training.trainer - INFO - Training completed
2025-05-29 11:28:31,697 - __main__ - INFO - Training completed successfully!
2025-05-29 11:28:31,697 - __main__ - INFO - Final Training Statistics:
2025-05-29 11:28:31,697 - __main__ - INFO -   Final epoch: 2
2025-05-29 11:28:31,697 - __main__ - INFO -   Total steps: 432
2025-05-29 11:28:31,697 - __main__ - INFO -   Best loss: 2.3779
2025-05-29 11:28:41,912 - src.training.trainer - INFO - Checkpoint saved: checkpoints\final_improved_model.pt
2025-05-29 11:28:41,912 - __main__ - INFO - Final model saved as 'final_improved_model.pt'
2025-05-29 11:28:41,912 - __main__ - INFO - Retraining completed!
