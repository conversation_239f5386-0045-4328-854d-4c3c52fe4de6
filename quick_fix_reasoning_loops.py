"""
Quick fix for reasoning loop issues.
This script implements immediate improvements to address the most critical problems.
"""

import os
import sys
import yaml
import torch
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from transformers import AutoTokenizer
from src.models.integrated_model import IntegratedLLMWithThinker


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    def convert_scientific_notation(obj):
        if isinstance(obj, dict):
            return {k: convert_scientific_notation(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_scientific_notation(item) for item in obj]
        elif isinstance(obj, str):
            try:
                if 'e-' in obj.lower() or 'e+' in obj.lower():
                    return float(obj)
                return obj
            except ValueError:
                return obj
        else:
            return obj

    return convert_scientific_notation(config)


def test_reasoning_diversity(model, tokenizer, prompts, logger):
    """Test if the model can generate diverse reasoning steps."""
    model.eval()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model.to(device)

    logger.info("Testing reasoning diversity...")
    
    for i, prompt in enumerate(prompts):
        logger.info(f"\nTest {i+1}: {prompt}")
        
        # Tokenize input
        input_ids = tokenizer.encode(prompt, return_tensors='pt').to(device)
        
        # Generate multiple times to check for diversity
        reasoning_outputs = []
        for trial in range(3):
            with torch.no_grad():
                result = model.generate(
                    input_ids=input_ids,
                    max_new_tokens=20,
                    temperature=0.8,  # Higher temperature for diversity
                    top_p=0.9,
                    force_thinking=True,
                    return_reasoning=True
                )
            
            if 'reasoning_info' in result and 'reasoning_steps' in result['reasoning_info']:
                reasoning_info = result['reasoning_info']
                reasoning_texts = model.thinker.generate_reasoning_text(
                    reasoning_info['reasoning_steps'],
                    tokenizer,
                    max_tokens_per_step=10,
                    stop_probabilities=reasoning_info.get('stop_probabilities')
                )
                reasoning_outputs.append(reasoning_texts)
                logger.info(f"  Trial {trial+1}: {reasoning_texts[:2]}")  # Show first 2 steps
        
        # Check for diversity
        if len(reasoning_outputs) > 1:
            first_steps = [r[0] if r else "" for r in reasoning_outputs]
            unique_steps = set(first_steps)
            diversity_ratio = len(unique_steps) / len(first_steps)
            logger.info(f"  Diversity ratio: {diversity_ratio:.2f} ({len(unique_steps)}/{len(first_steps)} unique)")
        else:
            logger.info("  No reasoning generated")


def apply_quick_fixes(model, logger):
    """Apply quick fixes to the model to address reasoning loops."""
    logger.info("Applying quick fixes to address reasoning loops...")
    
    # Fix 1: Add noise to ThinkerModule to break loops
    if hasattr(model, 'thinker') and hasattr(model.thinker, 'reasoning_layers'):
        for layer in model.thinker.reasoning_layers:
            if hasattr(layer, 'weight'):
                # Add small random noise to break deterministic patterns
                with torch.no_grad():
                    noise = torch.randn_like(layer.weight) * 0.001
                    layer.weight.add_(noise)
        logger.info("  ✓ Added noise to ThinkerModule layers")
    
    # Fix 2: Modify temperature settings for reasoning generation
    if hasattr(model, 'thinker'):
        # Increase temperature for reasoning generation
        model.thinker.reasoning_temperature = 1.2
        logger.info("  ✓ Increased reasoning temperature to 1.2")
    
    # Fix 3: Add randomization to decision mechanism
    if hasattr(model, 'decision_mechanism'):
        # Add some randomness to decision making
        model.decision_mechanism.add_noise = True
        logger.info("  ✓ Enabled noise in decision mechanism")
    
    return model


def main():
    logger = setup_logging()
    logger.info("Starting quick fix for reasoning loops...")

    # Load configuration
    config_path = 'config/model_config.yaml'
    config = load_config(config_path)

    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token

    # Test prompts
    test_prompts = [
        "The capital of France is",
        "To solve 2x + 5 = 13, we need to",
        "What is 2 + 2?",
    ]

    # Load and test original model
    logger.info("\n" + "="*60)
    logger.info("Testing Original Model")
    logger.info("="*60)
    
    original_model = IntegratedLLMWithThinker(config['model'])
    if os.path.exists('checkpoints/final_improved_model.pt'):
        checkpoint = torch.load('checkpoints/final_improved_model.pt', map_location='cpu')
        original_model.load_state_dict(checkpoint['model_state_dict'])
        logger.info("Loaded improved model checkpoint")
    
    test_reasoning_diversity(original_model, tokenizer, test_prompts, logger)
    
    # Apply quick fixes
    logger.info("\n" + "="*60)
    logger.info("Applying Quick Fixes")
    logger.info("="*60)
    
    fixed_model = apply_quick_fixes(original_model, logger)
    
    # Test fixed model
    logger.info("\n" + "="*60)
    logger.info("Testing Fixed Model")
    logger.info("="*60)
    
    test_reasoning_diversity(fixed_model, tokenizer, test_prompts, logger)
    
    # Save fixed model
    if os.path.exists('checkpoints/final_improved_model.pt'):
        checkpoint = torch.load('checkpoints/final_improved_model.pt', map_location='cpu')
        checkpoint['model_state_dict'] = fixed_model.state_dict()
        torch.save(checkpoint, 'checkpoints/quick_fixed_model.pt')
        logger.info("Saved quick-fixed model as 'checkpoints/quick_fixed_model.pt'")
    
    logger.info("\nQuick fix completed!")
    logger.info("\nRecommendations for next steps:")
    logger.info("1. Improve training data with more diverse reasoning examples")
    logger.info("2. Implement better stopping criteria for reasoning steps")
    logger.info("3. Add positional encoding to reasoning steps")
    logger.info("4. Use curriculum learning for progressive complexity")


if __name__ == '__main__':
    main()
