import json
import asyncio
import aiohttp
import random
import hashlib
import time
import logging
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass, asdict
from abc import ABC, abstractmethod
from pathlib import Path
import sqlite3
from contextlib import asynccontextmanager
import backoff

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('training_data_generator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class TrainingExample:
    text: str
    needs_thinking: bool
    reasoning: Optional[List[str]] = None
    provider_used: Optional[str] = None
    text_hash: Optional[str] = None
    
    def __post_init__(self):
        if self.text_hash is None:
            self.text_hash = hashlib.md5(self.text.encode()).hexdigest()

class DeduplicationManager:
    def __init__(self, db_path: str = "training_data.db"):
        self.db_path = db_path
        self.setup_database()
    
    def setup_database(self):
        """Initialize SQLite database for deduplication"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS processed_texts (
                text_hash TEXT PRIMARY KEY,
                text TEXT NOT NULL,
                needs_thinking BOOLEAN NOT NULL,
                reasoning TEXT,
                provider_used TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.commit()
        conn.close()
    
    def is_duplicate(self, text_hash: str) -> bool:
        """Check if text has already been processed"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT 1 FROM processed_texts WHERE text_hash = ?', (text_hash,))
        result = cursor.fetchone() is not None
        conn.close()
        return result
    
    def add_example(self, example: TrainingExample):
        """Add processed example to database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO processed_texts 
            (text_hash, text, needs_thinking, reasoning, provider_used)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            example.text_hash,
            example.text,
            example.needs_thinking,
            json.dumps(example.reasoning) if example.reasoning else None,
            example.provider_used
        ))
        conn.commit()
        conn.close()
    
    def get_processed_count(self) -> int:
        """Get total number of processed examples"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        cursor.execute('SELECT COUNT(*) FROM processed_texts')
        count = cursor.fetchone()[0]
        conn.close()
        return count
    
    def export_to_jsonl(self, output_file: str, limit: Optional[int] = None):
        """Export processed data to JSONL file"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = 'SELECT text, needs_thinking, reasoning FROM processed_texts'
        if limit:
            query += f' LIMIT {limit}'
        
        cursor.execute(query)
        
        with open(output_file, 'w') as f:
            for row in cursor.fetchall():
                text, needs_thinking, reasoning_json = row
                data = {
                    "text": text,
                    "needs_thinking": bool(needs_thinking)
                }
                if reasoning_json:
                    data["reasoning"] = json.loads(reasoning_json)
                f.write(json.dumps(data) + '\n')
        
        conn.close()

class LLMProvider(ABC):
    def __init__(self, api_key: str, model: str, rate_limit: float = 1.0):
        self.api_key = api_key
        self.model = model
        self.base_url = self.get_base_url()
        self.rate_limit = rate_limit  # requests per second
        self.last_request_time = 0
    
    @abstractmethod
    def get_base_url(self) -> str:
        pass
    
    @abstractmethod
    def get_headers(self) -> Dict[str, str]:
        pass
    
    @abstractmethod
    def format_request(self, prompt: str) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    def extract_response(self, response_data: Dict[str, Any]) -> str:
        pass
    
    async def wait_for_rate_limit(self):
        """Ensure we don't exceed rate limits"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        min_interval = 1.0 / self.rate_limit
        
        if time_since_last < min_interval:
            await asyncio.sleep(min_interval - time_since_last)
        
        self.last_request_time = time.time()

class OpenAIProvider(LLMProvider):
    def get_base_url(self) -> str:
        return "https://api.openai.com/v1/chat/completions"
    
    def get_headers(self) -> Dict[str, str]:
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def format_request(self, prompt: str) -> Dict[str, Any]:
        return {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 1000
        }
    
    def extract_response(self, response_data: Dict[str, Any]) -> str:
        return response_data["choices"][0]["message"]["content"]

class GeminiProvider(LLMProvider):
    def get_base_url(self) -> str:
        return f"https://generativelanguage.googleapis.com/v1beta/models/{self.model}:generateContent?key={self.api_key}"
    
    def get_headers(self) -> Dict[str, str]:
        return {"Content-Type": "application/json"}
    
    def format_request(self, prompt: str) -> Dict[str, Any]:
        return {
            "contents": [{
                "parts": [{"text": prompt}]
            }],
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 1000
            }
        }
    
    def extract_response(self, response_data: Dict[str, Any]) -> str:
        return response_data["candidates"][0]["content"]["parts"][0]["text"]

class GroqProvider(LLMProvider):
    def get_base_url(self) -> str:
        return "https://api.groq.com/openai/v1/chat/completions"
    
    def get_headers(self) -> Dict[str, str]:
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def format_request(self, prompt: str) -> Dict[str, Any]:
        return {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 1000
        }
    
    def extract_response(self, response_data: Dict[str, Any]) -> str:
        return response_data["choices"][0]["message"]["content"]

class DeepSeekProvider(LLMProvider):
    def get_base_url(self) -> str:
        return "https://api.deepseek.com/chat/completions"
    
    def get_headers(self) -> Dict[str, str]:
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def format_request(self, prompt: str) -> Dict[str, Any]:
        return {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 1000
        }
    
    def extract_response(self, response_data: Dict[str, Any]) -> str:
        return response_data["choices"][0]["message"]["content"]

class OpenRouterProvider(LLMProvider):
    def get_base_url(self) -> str:
        return "https://openrouter.ai/api/v1/chat/completions"
    
    def get_headers(self) -> Dict[str, str]:
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://your-app.com",
            "X-Title": "Training Data Generator"
        }
    
    def format_request(self, prompt: str) -> Dict[str, Any]:
        return {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 1000
        }
    
    def extract_response(self, response_data: Dict[str, Any]) -> str:
        return response_data["choices"][0]["message"]["content"]

class TogetherAIProvider(LLMProvider):
    def get_base_url(self) -> str:
        return "https://api.together.xyz/v1/chat/completions"
    
    def get_headers(self) -> Dict[str, str]:
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def format_request(self, prompt: str) -> Dict[str, Any]:
        return {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 1000
        }
    
    def extract_response(self, response_data: Dict[str, Any]) -> str:
        return response_data["choices"][0]["message"]["content"]

class TrainingDataGenerator:
    def __init__(self, max_concurrent: int = 10, checkpoint_interval: int = 100):
        self.providers = {}
        self.session = None
        self.max_concurrent = max_concurrent
        self.checkpoint_interval = checkpoint_interval
        self.dedup_manager = DeduplicationManager()
        self.stats = {
            'processed': 0,
            'duplicates_skipped': 0,
            'errors': 0,
            'start_time': time.time()
        }
    
    def add_provider(self, name: str, provider: LLMProvider):
        self.providers[name] = provider
        logger.info(f"Added provider: {name} (model: {provider.model})")
    
    async def __aenter__(self):
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=60)
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    @backoff.on_exception(
        backoff.expo,
        (aiohttp.ClientError, asyncio.TimeoutError),
        max_tries=3,
        max_time=300
    )
    async def call_llm(self, provider: LLMProvider, prompt: str) -> str:
        """Make an API call to the LLM provider with retry logic"""
        await provider.wait_for_rate_limit()
        
        try:
            request_data = provider.format_request(prompt)
            
            async with self.session.post(
                provider.base_url,
                headers=provider.get_headers(),
                json=request_data
            ) as response:
                if response.status == 200:
                    response_data = await response.json()
                    return provider.extract_response(response_data)
                elif response.status == 429:  # Rate limit
                    await asyncio.sleep(2)
                    raise aiohttp.ClientError("Rate limited")
                else:
                    error_text = await response.text()
                    raise Exception(f"API call failed with status {response.status}: {error_text}")
        
        except Exception as e:
            logger.error(f"Error calling LLM: {str(e)}")
            raise
    
    def get_classification_prompt(self, text: str) -> str:
        return f"""Analyze the following statement and determine if it requires step-by-step reasoning to understand or verify.

Statement: "{text}"

A statement needs thinking/reasoning if it involves:
- Mathematical calculations or word problems
- Multi-step logical reasoning or analysis
- Problem-solving processes
- Complex cause-and-effect relationships
- Questions requiring inference or deduction

A statement does NOT need thinking if it's:
- Simple factual statements
- Direct observations or definitions
- Basic knowledge or common facts
- Simple yes/no questions about facts

Respond with only "YES" if it needs step-by-step thinking, or "NO" if it doesn't."""

    def get_reasoning_prompt(self, text: str) -> str:
        return f"""For the following statement that requires step-by-step reasoning, provide the reasoning steps as a JSON array.

Statement: "{text}"

Break down the reasoning into clear, logical steps. Each step should be concise and build toward the solution or understanding.

Respond with only a JSON array of strings in this exact format:
["First step", "Second step", "Third step", "Final answer or conclusion"]

Make sure your response is valid JSON and contains 3-6 steps."""

    async def generate_single_example(self, text: str, provider_name: str = None) -> Optional[TrainingExample]:
        """Generate a single training example with deduplication"""
        text_hash = hashlib.md5(text.encode()).hexdigest()
        
        # Check for duplicates
        if self.dedup_manager.is_duplicate(text_hash):
            self.stats['duplicates_skipped'] += 1
            logger.debug(f"Skipping duplicate: {text[:50]}...")
            return None
        
        if provider_name is None:
            provider_name = random.choice(list(self.providers.keys()))
        
        provider = self.providers[provider_name]
        
        try:
            # Step 1: Classify if reasoning is needed
            classification_prompt = self.get_classification_prompt(text)
            classification_response = await self.call_llm(provider, classification_prompt)
            
            needs_thinking = "YES" in classification_response.strip().upper()
            
            reasoning = None
            if needs_thinking:
                # Step 2: Generate reasoning steps
                reasoning_prompt = self.get_reasoning_prompt(text)
                reasoning_response = await self.call_llm(provider, reasoning_prompt)
                
                try:
                    # Clean up response and try to parse as JSON
                    cleaned_response = reasoning_response.strip()
                    if cleaned_response.startswith('```json'):
                        cleaned_response = cleaned_response.replace('```json', '').replace('```', '').strip()
                    
                    reasoning = json.loads(cleaned_response)
                    if not isinstance(reasoning, list):
                        reasoning = [str(reasoning)]
                except json.JSONDecodeError:
                    # Fallback: create reasoning from response
                    lines = reasoning_response.strip().split('\n')
                    reasoning = [line.strip('- ').strip() for line in lines if line.strip()]
                    reasoning = reasoning[:6]  # Limit to 6 steps
            
            example = TrainingExample(
                text=text,
                needs_thinking=needs_thinking,
                reasoning=reasoning,
                provider_used=provider_name,
                text_hash=text_hash
            )
            
            # Save to database
            self.dedup_manager.add_example(example)
            self.stats['processed'] += 1
            
            return example
            
        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"Failed to generate example for '{text[:50]}...': {str(e)}")
            return None
    
    async def process_batch(self, texts: List[str], provider_name: str = None) -> List[TrainingExample]:
        """Process a batch of texts with concurrent limiting"""
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def process_with_semaphore(text):
            async with semaphore:
                return await self.generate_single_example(text, provider_name)
        
        tasks = [process_with_semaphore(text) for text in texts]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out None results and exceptions
        valid_results = []
        for result in results:
            if isinstance(result, TrainingExample):
                valid_results.append(result)
            elif isinstance(result, Exception):
                logger.error(f"Task exception: {result}")
        
        return valid_results
    
    async def generate_large_dataset(
        self, 
        input_file: str, 
        batch_size: int = 50,
        max_examples: Optional[int] = None,
        provider_rotation: bool = True
    ):
        """Generate training data from a large input file"""
        logger.info(f"Starting large dataset generation from {input_file}")
        logger.info(f"Batch size: {batch_size}, Max examples: {max_examples}")
        
        # Read input data
        texts = []
        if input_file.endswith('.jsonl'):
            with open(input_file, 'r') as f:
                for line in f:
                    data = json.loads(line.strip())
                    if isinstance(data, dict) and 'text' in data:
                        texts.append(data['text'])
                    elif isinstance(data, str):
                        texts.append(data)
        else:
            with open(input_file, 'r') as f:
                texts = [line.strip() for line in f if line.strip()]
        
        if max_examples:
            texts = texts[:max_examples]
        
        logger.info(f"Loaded {len(texts)} texts to process")
        
        # Process in batches
        provider_names = list(self.providers.keys()) if provider_rotation else [None]
        provider_idx = 0
        
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            current_provider = provider_names[provider_idx % len(provider_names)] if provider_rotation else None
            
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")
            logger.info(f"Using provider: {current_provider or 'random'}")
            
            await self.process_batch(batch, current_provider)
            
            if provider_rotation:
                provider_idx += 1
            
            # Checkpoint progress
            if (i + batch_size) % (self.checkpoint_interval * batch_size) == 0:
                self.log_progress()
        
        self.log_final_stats()
    
    def log_progress(self):
        """Log current progress and statistics"""
        elapsed = time.time() - self.stats['start_time']
        rate = self.stats['processed'] / elapsed if elapsed > 0 else 0
        
        logger.info(f"Progress Update:")
        logger.info(f"  Processed: {self.stats['processed']}")
        logger.info(f"  Duplicates skipped: {self.stats['duplicates_skipped']}")
        logger.info(f"  Errors: {self.stats['errors']}")
        logger.info(f"  Rate: {rate:.2f} examples/second")
        logger.info(f"  Total in DB: {self.dedup_manager.get_processed_count()}")
    
    def log_final_stats(self):
        """Log final statistics"""
        elapsed = time.time() - self.stats['start_time']
        total_processed = self.dedup_manager.get_processed_count()
        
        logger.info("=" * 50)
        logger.info("FINAL STATISTICS")
        logger.info("=" * 50)
        logger.info(f"Total time: {elapsed:.2f} seconds")
        logger.info(f"Examples processed this session: {self.stats['processed']}")
        logger.info(f"Duplicates skipped: {self.stats['duplicates_skipped']}")
        logger.info(f"Errors encountered: {self.stats['errors']}")
        logger.info(f"Total examples in database: {total_processed}")
        logger.info(f"Average rate: {self.stats['processed']/elapsed:.2f} examples/second")

def create_sample_input_file(filename: str, num_samples: int = 1000):
    """Create a sample input file for testing"""
    sample_templates = [
        "What is {a} + {b}?",
        "If a rectangle has length {a} and width {b}, what is its area?",
        "The capital of {country} is {city}.",
        "How many degrees are in a {shape}?",
        "What is {a}% of {b}?",
        "If you travel {a} miles in {b} hours, what is your speed?",
        "The human body has {a} bones.",
        "What is the square root of {a}?",
        "Water freezes at {a} degrees Celsius.",
        "How many sides does a {shape} have?"
    ]
    
    countries_cities = [
        ("France", "Paris"), ("Japan", "Tokyo"), ("Brazil", "Brasília"),
        ("Australia", "Canberra"), ("Egypt", "Cairo")
    ]
    
    shapes = ["triangle", "square", "pentagon", "hexagon", "octagon"]
    
    with open(filename, 'w') as f:
        for i in range(num_samples):
            template = random.choice(sample_templates)
            
            if "{country}" in template:
                country, city = random.choice(countries_cities)
                text = template.format(country=country, city=city)
            elif "{shape}" in template:
                shape = random.choice(shapes)
                text = template.format(shape=shape)
            else:
                a = random.randint(1, 100)
                b = random.randint(1, 100)
                text = template.format(a=a, b=b)
            
            f.write(text + '\n')

async def main():
    # Configuration
    config = {
        "openai": {
            "api_key": "********************************************************************************************************************************************************************",
            "model": "gpt-3.5-turbo",
            "rate_limit": 3.0  # requests per second
        },
        "gemini": {
            "api_key": "your-gemini-api-key", 
            "model": "gemini-pro",
            "rate_limit": 2.0
        },
        "groq": {
            "api_key": "your-groq-api-key",
            "model": "mixtral-8x7b-32768",
            "rate_limit": 10.0  # Groq is usually faster
        },
        "deepseek": {
            "api_key": "your-deepseek-api-key",
            "model": "deepseek-chat",
            "rate_limit": 1.0
        },
        "openrouter": {
            "api_key": "your-openrouter-api-key",
            "model": "microsoft/wizardlm-2-8x22b",
            "rate_limit": 1.0
        },
        "together": {
            "api_key": "your-together-api-key",
            "model": "meta-llama/Llama-2-70b-chat-hf",
            "rate_limit": 2.0
        }
    }
    
    # Create sample input file if it doesn't exist
    input_file = "sample_input.txt"
    if not Path(input_file).exists():
        logger.info("Creating sample input file...")
        create_sample_input_file(input_file, 200)  # Start with 200 samples
    
    async with TrainingDataGenerator(max_concurrent=15, checkpoint_interval=50) as generator:
        # Add providers (uncomment the ones you have API keys for)
        
        # Example: Add multiple providers with different rate limits
        generator.add_provider("openai", OpenAIProvider(config["openai"]["api_key"], config["openai"]["model"], config["openai"]["rate_limit"]))
        # generator.add_provider("groq", GroqProvider(config["groq"]["api_key"], config["groq"]["model"], config["groq"]["rate_limit"]))
        
        # For demo purposes
        if not generator.providers:
            logger.error("Please add your API keys to the config section and uncomment the providers you want to use.")
            return
        
        # Generate large dataset
        await generator.generate_large_dataset(
            input_file=input_file,
            batch_size=25,  # Process 25 texts at a time
            max_examples=100,  # Limit for testing
            provider_rotation=True  # Rotate between providers
        )
        
        # Export to JSONL
        output_file = f"training_data_{int(time.time())}.jsonl"
        generator.dedup_manager.export_to_jsonl(output_file)
        logger.info(f"Exported data to {output_file}")

if __name__ == "__main__":
    asyncio.run(main())