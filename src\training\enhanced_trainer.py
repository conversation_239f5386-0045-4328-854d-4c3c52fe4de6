"""
Enhanced trainer for the improved ThinkerModule architecture.
"""

import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from transformers import get_linear_schedule_with_warmup
from typing import Dict, List, Optional, Any
import logging
from tqdm import tqdm
import wandb

from .enhanced_loss_functions import EnhancedCombinedLoss


class EnhancedTrainer:
    """Enhanced trainer with sophisticated training strategies."""

    def __init__(self, model, config: Dict[str, Any], tokenizer=None):
        self.model = model
        self.config = config
        self.tokenizer = tokenizer
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Move model to device
        self.model.to(self.device)

        # Training configuration
        self.learning_rate = config['training']['learning_rate']
        self.num_epochs = config['training']['num_epochs']
        self.batch_size = config['training']['batch_size']
        self.gradient_accumulation_steps = config['training'].get('gradient_accumulation_steps', 1)
        self.max_grad_norm = config['training'].get('max_grad_norm', 1.0)

        # Enhanced loss function
        self.loss_fn = EnhancedCombinedLoss(
            vocab_size=model.vocab_size,
            config=config['training']
        )

        # Optimizer
        self.optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=self.learning_rate,
            weight_decay=config['training'].get('weight_decay', 0.01)
        )

        # Training state
        self.global_step = 0
        self.current_epoch = 0
        self.best_loss = float('inf')

        # Logging
        self.logger = logging.getLogger(__name__)
        self.use_wandb = config.get('use_wandb', False)

        # Training mode
        self.training_mode = config.get('training_mode', 'joint')  # 'joint', 'sequential', 'reasoning_only'

        # Curriculum learning
        self.use_curriculum = config.get('use_curriculum', False)
        self.curriculum_stage = 0
        self.curriculum_stages = config.get('curriculum_stages', 3)
        self.epochs_per_stage = config.get('epochs_per_stage', 2)

    def setup_scheduler(self, train_loader):
        """Setup learning rate scheduler."""
        total_steps = len(train_loader) * self.num_epochs // self.gradient_accumulation_steps
        warmup_steps = int(0.1 * total_steps)  # 10% warmup

        self.scheduler = get_linear_schedule_with_warmup(
            self.optimizer,
            num_warmup_steps=warmup_steps,
            num_training_steps=total_steps
        )

    def set_training_mode(self, mode: str):
        """Set training mode for different training strategies."""
        self.training_mode = mode
        self.model.set_training_mode(mode)

        if mode == 'reasoning_only':
            self.logger.info("Training mode: Reasoning components only")
        elif mode == 'joint':
            self.logger.info("Training mode: Joint training of all components")
        elif mode == 'sequential':
            self.logger.info("Training mode: Sequential training")

    def train_step(self, batch: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """Single training step."""
        self.model.train()

        # Move batch to device
        input_ids = batch['input_ids'].to(self.device)
        attention_mask = batch['attention_mask'].to(self.device)
        labels = batch['labels'].to(self.device)

        # Enhanced targets for supervised learning
        target_decisions = batch.get('decision_targets')
        if target_decisions is not None:
            target_decisions = target_decisions.to(self.device)

        # Reasoning targets for supervised learning
        reasoning_targets = batch.get('reasoning_targets')
        reasoning_lengths = batch.get('reasoning_lengths')

        if reasoning_targets is not None:
            reasoning_targets = reasoning_targets.to(self.device)
            reasoning_lengths = reasoning_lengths.to(self.device)

        # Process reasoning targets for loss calculation
        target_reasoning = None
        if reasoning_targets is not None and reasoning_lengths is not None:
            target_reasoning = reasoning_targets  # Already in correct format

        # Forward pass
        model_output = self.model(
            input_ids=input_ids,
            attention_mask=attention_mask,
            labels=labels,
            return_reasoning=True,
            tokenizer=self.tokenizer
        )

        # Compute losses
        losses = self.loss_fn(
            model_output=model_output,
            labels=labels,
            target_decisions=target_decisions,
            target_reasoning=target_reasoning
        )

        # Backward pass
        loss = losses['total_loss'] / self.gradient_accumulation_steps
        loss.backward()

        # Convert losses to float for logging
        step_losses = {k: v.item() if isinstance(v, torch.Tensor) else v
                      for k, v in losses.items()}

        return step_losses

    def train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """Train for one epoch."""
        self.model.train()

        # Update loss function epoch
        self.loss_fn.update_epoch(self.current_epoch)

        total_losses = {
            'total_loss': 0.0,
            'llm_loss': 0.0,
            'thinker_loss': 0.0,
            'decision_loss': 0.0,
            'projection_loss': 0.0
        }

        num_batches = 0
        progress_bar = tqdm(train_loader, desc=f"Epoch {self.current_epoch + 1}")

        for batch_idx, batch in enumerate(progress_bar):
            # Training step
            step_losses = self.train_step(batch)

            # Accumulate losses
            for key in total_losses:
                total_losses[key] += step_losses.get(key, 0.0)

            # Gradient accumulation
            if (batch_idx + 1) % self.gradient_accumulation_steps == 0:
                # Clip gradients
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)

                # Optimizer step
                self.optimizer.step()
                if hasattr(self, 'scheduler'):
                    self.scheduler.step()
                self.optimizer.zero_grad()

                self.global_step += 1

            num_batches += 1

            # Update progress bar
            avg_losses = {k: v / num_batches for k, v in total_losses.items()}
            progress_bar.set_postfix({
                'loss': f"{avg_losses['total_loss']:.4f}",
                'llm': f"{avg_losses['llm_loss']:.3f}",
                'thinker': f"{avg_losses['thinker_loss']:.3f}",
                'decision': f"{avg_losses['decision_loss']:.3f}"
            })

            # Logging
            if self.global_step % self.config['training'].get('logging_steps', 100) == 0:
                self.log_step(avg_losses)

        # Final gradient step if needed
        if len(train_loader) % self.gradient_accumulation_steps != 0:
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.max_grad_norm)
            self.optimizer.step()
            if hasattr(self, 'scheduler'):
                self.scheduler.step()
            self.optimizer.zero_grad()

        # Average losses for epoch
        avg_losses = {k: v / num_batches for k, v in total_losses.items()}
        return avg_losses

    def evaluate(self, eval_loader: DataLoader) -> Dict[str, float]:
        """Evaluate the model."""
        self.model.eval()

        total_losses = {
            'total_loss': 0.0,
            'llm_loss': 0.0,
            'thinker_loss': 0.0,
            'decision_loss': 0.0,
            'projection_loss': 0.0
        }

        num_batches = 0

        with torch.no_grad():
            for batch in tqdm(eval_loader, desc="Evaluating"):
                # Move batch to device
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)

                target_decisions = batch.get('target_decisions')
                if target_decisions is not None:
                    target_decisions = target_decisions.to(self.device)

                # Forward pass
                model_output = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels,
                    return_reasoning=True,
                    tokenizer=self.tokenizer
                )

                # Compute losses
                losses = self.loss_fn(
                    model_output=model_output,
                    labels=labels,
                    target_decisions=target_decisions,
                    target_reasoning=None
                )

                # Accumulate losses
                for key in total_losses:
                    total_losses[key] += losses.get(key, torch.tensor(0.0)).item()

                num_batches += 1

        # Average losses
        avg_losses = {k: v / num_batches for k, v in total_losses.items()}
        return avg_losses

    def train(self, train_loader: DataLoader, eval_loader: Optional[DataLoader] = None):
        """Main training loop."""
        self.logger.info("Starting enhanced training...")

        # Setup scheduler
        self.setup_scheduler(train_loader)

        # Training history
        history = {
            'train_losses': [],
            'eval_losses': []
        }

        for epoch in range(self.num_epochs):
            self.current_epoch = epoch

            # Train epoch
            train_losses = self.train_epoch(train_loader)
            history['train_losses'].append(train_losses)

            # Evaluate
            if eval_loader is not None:
                eval_losses = self.evaluate(eval_loader)
                history['eval_losses'].append(eval_losses)

                self.logger.info(f"Epoch {epoch + 1}/{self.num_epochs}")
                self.logger.info(f"  Train Loss: {train_losses['total_loss']:.4f}")
                self.logger.info(f"  Eval Loss: {eval_losses['total_loss']:.4f}")

                # Save best model
                if eval_losses['total_loss'] < self.best_loss:
                    self.best_loss = eval_losses['total_loss']
                    self.save_checkpoint('best_enhanced_model')
            else:
                self.logger.info(f"Epoch {epoch + 1}/{self.num_epochs}")
                self.logger.info(f"  Train Loss: {train_losses['total_loss']:.4f}")

                # Save best model based on train loss
                if train_losses['total_loss'] < self.best_loss:
                    self.best_loss = train_losses['total_loss']
                    self.save_checkpoint('best_enhanced_model')

            # Log epoch results
            self.log_epoch(epoch, train_losses, eval_losses if eval_loader else None)

        self.logger.info("Training completed!")
        return history

    def train_with_curriculum(self, curriculum_loader, eval_loader: Optional[DataLoader] = None):
        """Train with curriculum learning strategy."""
        self.logger.info("Starting curriculum learning training...")

        history = {
            'train_losses': [],
            'eval_losses': [],
            'curriculum_stages': []
        }

        total_epochs_completed = 0

        for stage in range(self.curriculum_stages):
            self.curriculum_stage = stage
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"CURRICULUM STAGE {stage + 1}/{self.curriculum_stages}")
            self.logger.info(f"{'='*60}")

            # Create data loader for this stage
            stage_loader = curriculum_loader.create_curriculum_loader(stage, self.curriculum_stages)

            # Setup scheduler for this stage
            self.setup_scheduler(stage_loader)

            # Train for specified epochs in this stage
            stage_history = {
                'train_losses': [],
                'eval_losses': []
            }

            for epoch in range(self.epochs_per_stage):
                self.current_epoch = total_epochs_completed + epoch

                # Update loss function epoch
                self.loss_fn.update_epoch(self.current_epoch)

                # Train epoch
                train_losses = self.train_epoch(stage_loader)
                stage_history['train_losses'].append(train_losses)

                # Evaluate
                if eval_loader is not None:
                    eval_losses = self.evaluate(eval_loader)
                    stage_history['eval_losses'].append(eval_losses)

                    self.logger.info(f"Stage {stage + 1}, Epoch {epoch + 1}/{self.epochs_per_stage}")
                    self.logger.info(f"  Train Loss: {train_losses['total_loss']:.4f}")
                    self.logger.info(f"  Eval Loss: {eval_losses['total_loss']:.4f}")

                    # Save best model
                    if eval_losses['total_loss'] < self.best_loss:
                        self.best_loss = eval_losses['total_loss']
                        self.save_checkpoint(f'best_curriculum_stage_{stage}')
                else:
                    self.logger.info(f"Stage {stage + 1}, Epoch {epoch + 1}/{self.epochs_per_stage}")
                    self.logger.info(f"  Train Loss: {train_losses['total_loss']:.4f}")

                    if train_losses['total_loss'] < self.best_loss:
                        self.best_loss = train_losses['total_loss']
                        self.save_checkpoint(f'best_curriculum_stage_{stage}')

            # Record stage history
            history['curriculum_stages'].append(stage_history)
            history['train_losses'].extend(stage_history['train_losses'])
            if stage_history['eval_losses']:
                history['eval_losses'].extend(stage_history['eval_losses'])

            total_epochs_completed += self.epochs_per_stage

            self.logger.info(f"Completed curriculum stage {stage + 1}")

        # Save final curriculum model
        self.save_checkpoint('final_curriculum_model')
        self.logger.info("Curriculum learning completed!")

        return history

    def log_step(self, losses: Dict[str, float]):
        """Log step-level metrics."""
        if self.use_wandb:
            log_dict = {f'train/step_{k}': v for k, v in losses.items()}
            log_dict['train/global_step'] = self.global_step
            log_dict['train/learning_rate'] = self.optimizer.param_groups[0]['lr']
            wandb.log(log_dict)

    def log_epoch(self, epoch: int, train_losses: Dict[str, float], eval_losses: Optional[Dict[str, float]]):
        """Log epoch-level metrics."""
        if self.use_wandb:
            log_dict = {f'train/epoch_{k}': v for k, v in train_losses.items()}
            log_dict['epoch'] = epoch

            if eval_losses:
                log_dict.update({f'eval/epoch_{k}': v for k, v in eval_losses.items()})

            wandb.log(log_dict)

    def save_checkpoint(self, name: str):
        """Save model checkpoint."""
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epoch': self.current_epoch,
            'global_step': self.global_step,
            'best_loss': self.best_loss,
            'config': self.config
        }

        if hasattr(self, 'scheduler'):
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()

        checkpoint_path = f'checkpoints/{name}.pt'
        os.makedirs('checkpoints', exist_ok=True)
        torch.save(checkpoint, checkpoint_path)
        self.logger.info(f"Checkpoint saved: {checkpoint_path}")

    def get_training_info(self):
        """Get current training information."""
        return {
            'current_epoch': self.current_epoch,
            'global_step': self.global_step,
            'best_loss': self.best_loss,
            'training_mode': self.training_mode
        }
