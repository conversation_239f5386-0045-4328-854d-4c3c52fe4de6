"""
Training script for the enhanced ThinkerModule architecture.
"""

import os
import sys
import yaml
import torch
import logging
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from transformers import AutoTokenizer
from src.models.enhanced_integrated_model import <PERSON>hanced<PERSON>ntegra<PERSON><PERSON><PERSON><PERSON><PERSON>Thin<PERSON>
from src.training.enhanced_trainer import EnhancedTrainer
from src.training.data_loader import DataLoader


def setup_logging():
    """Setup logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('enhanced_training.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Convert string scientific notation to float
    def convert_scientific_notation(obj):
        if isinstance(obj, dict):
            return {k: convert_scientific_notation(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_scientific_notation(item) for item in obj]
        elif isinstance(obj, str):
            try:
                if 'e-' in obj.lower() or 'e+' in obj.lower():
                    return float(obj)
                return obj
            except ValueError:
                return obj
        else:
            return obj

    return convert_scientific_notation(config)


def create_enhanced_config():
    """Create enhanced configuration for the new architecture."""
    config = {
        'model': {
            'thinker': {
                'max_reasoning_steps': 12,
                'num_reasoning_layers': 4,
                'd_model': 768,
                'vocab_size': 50257
            },
            'num_integration_layers': 3
        },
        'training': {
            'batch_size': 4,
            'learning_rate': 5e-5,
            'num_epochs': 5,
            'gradient_accumulation_steps': 2,
            'max_grad_norm': 1.0,
            'weight_decay': 0.01,
            'logging_steps': 50,
            
            # Enhanced loss weights
            'llm_loss_weight': 1.0,
            'thinker_loss_weight': 0.1,
            'decision_loss_weight': 0.15,
            'projection_loss_weight': 0.05,
            
            # Progressive training
            'use_progressive_weights': True,
            'training_mode': 'joint'
        },
        'data': {
            'train_file': 'data/train.jsonl',
            'eval_file': 'data/eval.jsonl',
            'max_length': 512
        },
        'use_wandb': False
    }
    
    return config


def validate_training_data(data_path: str, logger):
    """Validate that training data exists and is properly formatted."""
    if not os.path.exists(data_path):
        logger.error(f"Training data not found: {data_path}")
        return False
    
    # Check first few lines to ensure proper format
    try:
        import json
        with open(data_path, 'r') as f:
            for i, line in enumerate(f):
                if i >= 3:  # Check first 3 lines
                    break
                data = json.loads(line.strip())
                if 'text' not in data:
                    logger.error(f"Invalid data format in line {i+1}: missing 'text' field")
                    return False
        logger.info(f"Training data validation passed: {data_path}")
        return True
    except Exception as e:
        logger.error(f"Error validating training data: {e}")
        return False


def main():
    logger = setup_logging()
    logger.info("Starting enhanced ThinkerModule training...")

    # Create enhanced configuration
    config = create_enhanced_config()
    logger.info("Enhanced configuration created")

    # Validate training data
    train_file = config['data']['train_file']
    if not validate_training_data(train_file, logger):
        logger.error("Training data validation failed. Exiting.")
        return

    # Initialize tokenizer
    logger.info("Initializing tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained('gpt2')
    tokenizer.pad_token = tokenizer.eos_token

    # Initialize enhanced model
    logger.info("Initializing enhanced model...")
    model = EnhancedIntegratedLLMWithThinker(config)
    
    # Show model info
    model_info = model.get_model_info()
    logger.info(f"Enhanced model initialized with {model_info['total_params']:,} parameters")
    logger.info(f"  Base LLM: {model_info['component_info']['llm_params']:,}")
    logger.info(f"  Enhanced ThinkerModule: {model_info['component_info']['thinker_params']:,}")
    logger.info(f"  Enhanced Decision Mechanism: {model_info['component_info']['decision_params']:,}")
    logger.info(f"  Integration Components: {model_info['component_info']['integration_params']:,}")

    # Create data loaders
    logger.info("Creating data loaders...")
    data_loader = DataLoader(config, tokenizer)
    train_loader = data_loader.create_train_loader()
    eval_loader = data_loader.create_eval_loader()

    logger.info(f"Training batches: {len(train_loader)}")
    if eval_loader:
        logger.info(f"Evaluation batches: {len(eval_loader)}")

    # Initialize enhanced trainer
    logger.info("Initializing enhanced trainer...")
    trainer = EnhancedTrainer(model, config, tokenizer)

    # Log training configuration
    logger.info("Enhanced Training Configuration:")
    logger.info(f"  Batch size: {config['training']['batch_size']}")
    logger.info(f"  Gradient accumulation: {config['training']['gradient_accumulation_steps']}")
    logger.info(f"  Learning rate: {config['training']['learning_rate']}")
    logger.info(f"  Number of epochs: {config['training']['num_epochs']}")
    logger.info(f"  Enhanced loss weights:")
    logger.info(f"    LLM: {config['training']['llm_loss_weight']}")
    logger.info(f"    Thinker: {config['training']['thinker_loss_weight']}")
    logger.info(f"    Decision: {config['training']['decision_loss_weight']}")
    logger.info(f"    Projection: {config['training']['projection_loss_weight']}")
    logger.info(f"  Progressive weighting: {config['training']['use_progressive_weights']}")

    # Start training
    logger.info("Starting enhanced training...")
    try:
        history = trainer.train(train_loader, eval_loader)
        logger.info("Enhanced training completed successfully!")
        
        # Log final statistics
        final_stats = trainer.get_training_info()
        logger.info("Final Training Statistics:")
        logger.info(f"  Final epoch: {final_stats['current_epoch']}")
        logger.info(f"  Total steps: {final_stats['global_step']}")
        logger.info(f"  Best loss: {final_stats['best_loss']:.4f}")
        logger.info(f"  Training mode: {final_stats['training_mode']}")
        
        # Save final model
        trainer.save_checkpoint('final_enhanced_model')
        logger.info("Final enhanced model saved as 'final_enhanced_model.pt'")
        
        # Test different training modes
        logger.info("\nTesting reasoning-only training mode...")
        trainer.set_training_mode('reasoning_only')
        
        # Train for 1 more epoch with reasoning-only mode
        config['training']['num_epochs'] = 1
        trainer.num_epochs = 1
        trainer.current_epoch = 0
        
        reasoning_history = trainer.train(train_loader, eval_loader)
        trainer.save_checkpoint('reasoning_only_enhanced_model')
        logger.info("Reasoning-only model saved as 'reasoning_only_enhanced_model.pt'")
        
    except Exception as e:
        logger.error(f"Enhanced training failed with error: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return

    logger.info("Enhanced training pipeline completed!")


if __name__ == '__main__':
    main()
