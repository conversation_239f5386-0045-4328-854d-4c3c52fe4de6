"""
Enhanced Decision Mechanism with sophisticated complexity analysis.
This determines when to use reasoning vs. direct generation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any


class EnhancedDecisionMechanism(nn.Module):
    """Enhanced decision mechanism with better complexity analysis."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.d_model = config.get('d_model', 768)
        self.vocab_size = config.get('vocab_size', 50257)

        # Feature extractors
        self.length_analyzer = nn.Linear(1, 64)
        self.token_diversity_analyzer = nn.Linear(1, 64)
        self.semantic_analyzer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(self.d_model, nhead=8, batch_first=True),
            num_layers=2
        )

        # Pattern recognition for different question types
        self.pattern_analyzer = nn.Sequential(
            nn.Linear(self.d_model, 512),
            nn.<PERSON><PERSON>(),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 64)  # Pattern features
        )

        # Decision network
        self.decision_network = nn.Sequential(
            nn.Linear(self.d_model + 128 + 64, 512),  # semantic + length + diversity + pattern features
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )

        # Confidence estimator
        self.confidence_estimator = nn.Sequential(
            nn.Linear(self.d_model + 128 + 64, 256),
            nn.ReLU(),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )

        # Add noise for exploration
        self.add_noise = False
        self.noise_scale = 0.1

    def extract_features(self, input_ids, attention_mask=None, input_embeddings=None):
        """Extract comprehensive features for decision making."""
        batch_size = input_ids.size(0)
        seq_len = input_ids.size(1)
        device = input_ids.device

        # Length features
        if attention_mask is not None:
            actual_lengths = attention_mask.sum(dim=1).float()
        else:
            actual_lengths = torch.full((batch_size,), seq_len, dtype=torch.float, device=device)

        # Normalize length (longer sequences are more complex)
        normalized_lengths = torch.clamp(actual_lengths / 100.0, 0, 1)  # Normalize by 100 tokens
        length_features = self.length_analyzer(normalized_lengths.unsqueeze(-1))

        # Token diversity features
        unique_tokens = []
        for i in range(batch_size):
            if attention_mask is not None:
                valid_tokens = input_ids[i, attention_mask[i].bool()]
            else:
                valid_tokens = input_ids[i]

            if len(valid_tokens) > 0:
                diversity = len(torch.unique(valid_tokens)) / len(valid_tokens)
            else:
                diversity = 0.0
            unique_tokens.append(diversity)

        diversity_tensor = torch.tensor(unique_tokens, device=device).unsqueeze(-1)
        diversity_features = self.token_diversity_analyzer(diversity_tensor)

        # Semantic features
        if input_embeddings is not None:
            # Handle attention mask for transformer encoder
            if attention_mask is not None:
                src_key_padding_mask = ~attention_mask.bool()
            else:
                src_key_padding_mask = None

            semantic_features = self.semantic_analyzer(input_embeddings, src_key_padding_mask=src_key_padding_mask)

            if attention_mask is not None:
                # Masked average pooling
                mask_expanded = attention_mask.unsqueeze(-1).expand_as(semantic_features).float()
                semantic_features_masked = semantic_features * mask_expanded
                semantic_pooled = semantic_features_masked.sum(dim=1) / attention_mask.sum(dim=1, keepdim=True).float()
            else:
                semantic_pooled = semantic_features.mean(dim=1)
        else:
            semantic_pooled = torch.zeros(batch_size, self.d_model, device=device)

        # Pattern features (detect question types, math problems, etc.)
        pattern_features = self.pattern_analyzer(semantic_pooled)

        # Combine features
        combined_features = torch.cat([semantic_pooled, length_features, diversity_features, pattern_features], dim=-1)

        return combined_features

    def detect_complexity_patterns(self, input_ids, tokenizer=None):
        """Detect specific patterns that indicate complexity."""
        complexity_indicators = {
            'math_keywords': ['calculate', 'solve', 'equation', 'formula', '+', '-', '*', '/', '='],
            'reasoning_keywords': ['because', 'therefore', 'if', 'then', 'why', 'how', 'explain'],
            'complex_questions': ['what is the', 'how do you', 'why does', 'explain the'],
            'numbers': ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']
        }

        if tokenizer is None:
            return 0.5  # Default complexity if no tokenizer

        # Decode input to text
        text = tokenizer.decode(input_ids[0], skip_special_tokens=True).lower()

        complexity_score = 0.0
        total_indicators = 0

        for category, keywords in complexity_indicators.items():
            category_score = sum(1 for keyword in keywords if keyword in text)
            if category == 'math_keywords' and category_score > 0:
                complexity_score += 0.4  # Math problems are complex
            elif category == 'reasoning_keywords' and category_score > 0:
                complexity_score += 0.3  # Reasoning questions are complex
            elif category == 'complex_questions' and category_score > 0:
                complexity_score += 0.2  # Complex question patterns
            elif category == 'numbers' and category_score > 2:
                complexity_score += 0.1  # Multiple numbers suggest calculation

            total_indicators += 1

        return min(complexity_score, 1.0)

    def forward(self, input_ids, attention_mask=None, input_embeddings=None, tokenizer=None):
        """Make decision whether to use reasoning."""
        features = self.extract_features(input_ids, attention_mask, input_embeddings)

        # Decision probability
        decision_prob = self.decision_network(features)

        # Confidence in decision
        confidence = self.confidence_estimator(features)

        # Add pattern-based complexity boost
        if tokenizer is not None:
            pattern_complexity = self.detect_complexity_patterns(input_ids, tokenizer)
            # Boost decision probability for complex patterns
            decision_prob = decision_prob + pattern_complexity * 0.3
            decision_prob = torch.clamp(decision_prob, 0, 1)

        # Add exploration noise if enabled
        if self.add_noise and self.training:
            noise = torch.randn_like(decision_prob) * self.noise_scale
            decision_prob = torch.clamp(decision_prob + noise, 0, 1)

        return {
            'decision_probs': decision_prob,
            'confidence': confidence,
            'features': features
        }


class AdaptiveDecisionMechanism(nn.Module):
    """Adaptive decision mechanism that learns from feedback."""

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.base_decision = EnhancedDecisionMechanism(config)

        # Feedback learning components
        self.feedback_memory = []
        self.max_memory_size = 1000

        # Adaptation network
        self.adaptation_network = nn.Sequential(
            nn.Linear(config.get('d_model', 768) + 3, 256),  # features + decision + confidence + feedback
            nn.ReLU(),
            nn.Linear(256, 1),
            nn.Tanh()  # Adjustment factor
        )

    def forward(self, input_ids, attention_mask=None, input_embeddings=None, tokenizer=None):
        """Make adaptive decision based on past feedback."""
        # Get base decision
        base_output = self.base_decision(input_ids, attention_mask, input_embeddings, tokenizer)

        # Apply adaptation if we have feedback history
        if len(self.feedback_memory) > 10:  # Need some history
            # Calculate adaptation based on recent feedback
            recent_feedback = self.feedback_memory[-10:]  # Last 10 decisions
            avg_feedback = sum(recent_feedback) / len(recent_feedback)

            # Adjust decision based on feedback
            if avg_feedback < 0.5:  # Poor recent performance
                # Be more conservative about using reasoning
                adjustment = -0.1
            else:  # Good recent performance
                # Be more confident in decisions
                adjustment = 0.1

            adjusted_prob = torch.clamp(base_output['decision_probs'] + adjustment, 0, 1)
            base_output['decision_probs'] = adjusted_prob

        return base_output

    def add_feedback(self, decision_was_correct: bool, quality_score: float = None):
        """Add feedback about decision quality."""
        if quality_score is not None:
            feedback_score = quality_score
        else:
            feedback_score = 1.0 if decision_was_correct else 0.0

        self.feedback_memory.append(feedback_score)

        # Limit memory size
        if len(self.feedback_memory) > self.max_memory_size:
            self.feedback_memory = self.feedback_memory[-self.max_memory_size:]

    def get_decision_stats(self):
        """Get statistics about decision performance."""
        if not self.feedback_memory:
            return {'avg_quality': 0.0, 'num_decisions': 0}

        return {
            'avg_quality': sum(self.feedback_memory) / len(self.feedback_memory),
            'num_decisions': len(self.feedback_memory),
            'recent_quality': sum(self.feedback_memory[-10:]) / min(10, len(self.feedback_memory))
        }
