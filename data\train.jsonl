{"text": "The capital of France is Paris. It is known for the Eiffel Tower.", "needs_thinking": false}
{"text": "To solve the equation 2x + 5 = 13, we need to isolate x. First, subtract 5 from both sides: 2x = 8. Then divide by 2: x = 4.", "needs_thinking": true, "reasoning": ["Start with equation 2x + 5 = 13", "Subtract 5 from both sides", "Get 2x = 8", "Divide both sides by 2", "Final answer: x = 4"]}
{"text": "The weather is nice today.", "needs_thinking": false}
{"text": "If a train travels at 60 mph for 2.5 hours, how far does it go? Distance = speed × time = 60 × 2.5 = 150 miles.", "needs_thinking": true, "reasoning": ["Identify the formula: Distance = speed × time", "Substitute values: speed = 60 mph, time = 2.5 hours", "Calculate: 60 × 2.5 = 150", "Answer: 150 miles"]}
{"text": "Mount Everest is the highest mountain on Earth.", "needs_thinking": false}
{"text": "Water freezes at 0 degrees Celsius.", "needs_thinking": false}
{"text": "If you have 15 apples and give away 1/3 of them, how many apples do you have left?", "needs_thinking": true, "reasoning": ["Start with total apples: 15", "Calculate 1/3 of 15: 15 ÷ 3 = 5 apples to give away", "Subtract given apples from total: 15 - 5 = 10", "Answer: 10 apples left"]}
{"text": "The Earth rotates around the Sun.", "needs_thinking": false}
{"text": "If a rectangle has length 8 cm and width 5 cm, what is its area and perimeter?", "needs_thinking": true, "reasoning": ["Calculate area: length × width = 8 × 5 = 40 square cm", "Calculate perimeter: 2 × (length + width) = 2 × (8 + 5) = 2 × 13 = 26 cm", "Final answer: Area = 40 sq cm, Perimeter = 26 cm"]}
{"text": "Dolphins are mammals, not fish.", "needs_thinking": false}
{"text": "If it takes 6 hours to paint a fence and 2 people work together, how long will it take?", "needs_thinking": true, "reasoning": ["Original time for one person: 6 hours", "With 2 people, work is split equally", "Divide total time by number of people: 6 ÷ 2 = 3", "Answer: 3 hours"]}
{"text": "The human body has 206 bones.", "needs_thinking": false}
{"text": "What percentage of 80 is 20?", "needs_thinking": true, "reasoning": ["Set up equation: 20 is what percent of 80", "Formula: percent = (part ÷ whole) × 100", "Calculate: (20 ÷ 80) × 100", "Simplify: 0.25 × 100 = 25", "Answer: 25%"]}
{"text": "Python is a programming language.", "needs_thinking": false}
{"text": "If you have a sequence 2, 4, 8, 16, what's the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: each number is doubled", "Last number is 16", "Multiple 16 by 2", "Answer: 32"]}
{"text": "The Pacific Ocean is the largest ocean.", "needs_thinking": false}
{"text": "If a triangle has angles of 45° and 60°, what is the third angle?", "needs_thinking": true, "reasoning": ["Recall: angles in a triangle sum to 180°", "Add known angles: 45° + 60° = 105°", "Subtract from 180°: 180° - 105° = 75°", "Answer: 75°"]}
{"text": "Rome is the capital of Italy.", "needs_thinking": false}
{"text": "If a box contains 3 red marbles, 4 blue marbles, and 5 green marbles, what is the probability of picking a blue marble?", "needs_thinking": true, "reasoning": ["Count total marbles: 3 + 4 + 5 = 12", "Count favorable outcomes (blue marbles): 4", "Calculate probability: 4/12", "Simplify: 1/3", "Answer: 1/3 or approximately 0.333"]}
{"text": "Gold is a precious metal.", "needs_thinking": false}
{"text": "If you save $100 per month for a year with 5% annual interest, how much will you have?", "needs_thinking": true, "reasoning": ["Principal = $100 × 12 = $1200", "Interest rate = 5% = 0.05", "Simple interest = Principal × rate = 1200 × 0.05 = $60", "Total = Principal + Interest = 1200 + 60 = $1260", "Answer: $1,260"]}
{"text": "The Sun is a star.", "needs_thinking": false}
{"text": "If 3x + 2 = 14, what is the value of 2x - 1?", "needs_thinking": true, "reasoning": ["Solve for x first: 3x + 2 = 14", "Subtract 2: 3x = 12", "Divide by 3: x = 4", "Now substitute x = 4 into 2x - 1", "Calculate: 2(4) - 1 = 8 - 1 = 7", "Answer: 7"]}
{"text": "Venus is the second planet from the Sun.", "needs_thinking": false}
{"text": "If a car accelerates from 0 to 60 mph in 5 seconds, what is its average acceleration?", "needs_thinking": true, "reasoning": ["Convert 60 mph to feet per second: 60 × 5280/3600 = 88 fps", "Initial velocity = 0 fps, Final velocity = 88 fps", "Time = 5 seconds", "Acceleration = (final velocity - initial velocity)/time", "Calculate: (88 - 0)/5 = 17.6", "Answer: 17.6 feet per second squared"]}
{"text": "The Amazon River is the largest river by discharge volume.", "needs_thinking": false}
{"text": "If a recipe calls for 2 cups of flour for 12 cookies, how much flour is needed for 30 cookies?", "needs_thinking": true, "reasoning": ["Find flour per cookie: 2 cups / 12 cookies = 1/6 cup per cookie", "Multiply by desired cookies: (1/6) × 30 = 5", "Answer: 5 cups of flour"]}
{"text": "The speed of light in a vacuum is approximately 299,792,458 meters per second.", "needs_thinking": false}
{"text": "If a store offers a 20% discount on an item priced at $75, what is the final price?", "needs_thinking": true, "reasoning": ["Calculate discount amount: 20% of $75 = 0.20 × 75 = $15", "Subtract discount from original price: $75 - $15 = $60", "Answer: $60"]}
{"text": "The square root of 144 is 12.", "needs_thinking": false}
{"text": "If you flip a fair coin three times, what is the probability of getting exactly two heads?", "needs_thinking": true, "reasoning": ["List all possible outcomes: HHH, HHT, HTH, THH, HTT, THT, TTH, TTT (8 total outcomes)", "Identify outcomes with exactly two heads: HHT, HTH, THH (3 outcomes)", "Calculate probability: 3/8", "Answer: 3/8"]}
{"text": "The capital of Canada is Ottawa.", "needs_thinking": false}
{"text": "If a car travels 180 miles on 6 gallons of gas, what is its fuel efficiency in miles per gallon?", "needs_thinking": true, "reasoning": ["Divide total miles by total gallons: 180 miles / 6 gallons", "Calculate: 30", "Answer: 30 miles per gallon"]}
{"text": "The chemical symbol for water is H2O.", "needs_thinking": false}
{"text": "If a sequence is defined by an = 2n + 1, what is the 5th term?", "needs_thinking": true, "reasoning": ["Substitute n = 5 into the formula: a5 = 2(5) + 1", "Calculate: 10 + 1 = 11", "Answer: 11"]}
{"text": "The largest planet in our solar system is Jupiter.", "needs_thinking": false}
{"text": "If a clock shows 3:00 PM, what time will it be in 7 hours?", "needs_thinking": true, "reasoning": ["Start time: 3:00 PM", "Add 7 hours: 3 + 7 = 10", "Answer: 10:00 PM"]}
{"text": "The currency of Japan is the Japanese Yen.", "needs_thinking": false}
{"text": "If a right-angled triangle has sides of length 3 and 4, what is the length of the hypotenuse?", "needs_thinking": true, "reasoning": ["Use Pythagorean theorem: a^2 + b^2 = c^2", "Substitute sides: 3^2 + 4^2 = c^2", "Calculate: 9 + 16 = c^2", "25 = c^2", "Take square root: c = 5", "Answer: 5"]}
{"text": "The human heart has four chambers.", "needs_thinking": false}
{"text": "If you have 5 red shirts, 3 blue shirts, and 2 green shirts, what is the ratio of red shirts to total shirts?", "needs_thinking": true, "reasoning": ["Count red shirts: 5", "Count total shirts: 5 + 3 + 2 = 10", "Form the ratio: 5/10", "Simplify: 1/2", "Answer: 1:2 or 1/2"]}
{"text": "The capital of Australia is Canberra.", "needs_thinking": false}
{"text": "If a baker makes 100 cupcakes and sells 75% of them, how many cupcakes are left?", "needs_thinking": true, "reasoning": ["Calculate sold cupcakes: 75% of 100 = 0.75 × 100 = 75", "Subtract sold from total: 100 - 75 = 25", "Answer: 25 cupcakes left"]}
{"text": "The smallest prime number is 2.", "needs_thinking": false}
{"text": "If a sequence starts with 1, 1, and each subsequent number is the sum of the two preceding ones (Fibonacci sequence), what is the 7th number?", "needs_thinking": true, "reasoning": ["Sequence: 1, 1", "3rd: 1+1=2", "4th: 1+2=3", "5th: 2+3=5", "6th: 3+5=8", "7th: 5+8=13", "Answer: 13"]}
{"text": "The currency of the United States is the US Dollar.", "needs_thinking": false}
{"text": "If a circle has a radius of 7 cm, what is its circumference? (Use π ≈ 22/7)", "needs_thinking": true, "reasoning": ["Formula for circumference: C = 2πr", "Substitute values: C = 2 × (22/7) × 7", "Calculate: C = 2 × 22 = 44", "Answer: 44 cm"]}
{"text": "The largest desert in the world is the Sahara Desert.", "needs_thinking": false}
{"text": "The capital of Brazil is Brasília.", "needs_thinking": false}
{"text": "If a car travels at an average speed of 70 km/h for 4 hours, how far does it travel?", "needs_thinking": true, "reasoning": ["Distance = speed × time", "Substitute values: speed = 70 km/h, time = 4 hours", "Calculate: 70 × 4 = 280", "Answer: 280 km"]}
{"text": "The chemical symbol for oxygen is O.", "needs_thinking": false}
{"text": "If a recipe requires 3 eggs for 24 muffins, how many eggs are needed for 48 muffins?", "needs_thinking": true, "reasoning": ["Find eggs per muffin: 3 eggs / 24 muffins = 1/8 egg per muffin", "Multiply by desired muffins: (1/8) × 48 = 6", "Answer: 6 eggs"]}
{"text": "The deepest ocean trench is the Mariana Trench.", "needs_thinking": false}
{"text": "What is the value of x in the equation 5x - 10 = 15?", "needs_thinking": true, "reasoning": ["Start with equation 5x - 10 = 15", "Add 10 to both sides: 5x = 25", "Divide by 5: x = 5", "Answer: x = 5"]}
{"text": "The largest continent by area is Asia.", "needs_thinking": false}
{"text": "If a shirt costs $30 and is discounted by 15%, what is the final price?", "needs_thinking": true, "reasoning": ["Calculate discount amount: 15% of $30 = 0.15 × 30 = $4.50", "Subtract discount from original price: $30 - $4.50 = $25.50", "Answer: $25.50"]}
{"text": "The capital of Egypt is Cairo.", "needs_thinking": false}
{"text": "If a sequence is 1, 4, 9, 16, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: numbers are squares of integers (1^2, 2^2, 3^2, 4^2)", "Next integer is 5", "Calculate 5^2", "Answer: 25"]}
{"text": "The human skeleton has 206 bones.", "needs_thinking": false}
{"text": "If a car travels 240 miles in 4 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 240 miles, Time = 4 hours", "Calculate: 240 / 4 = 60", "Answer: 60 mph"]}
{"text": "The chemical symbol for sodium is Na.", "needs_thinking": false}
{"text": "If a right-angled triangle has legs of 6 cm and 8 cm, what is the length of the hypotenuse?", "needs_thinking": true, "reasoning": ["Use Pythagorean theorem: a^2 + b^2 = c^2", "Substitute legs: 6^2 + 8^2 = c^2", "Calculate: 36 + 64 = c^2", "100 = c^2", "Take square root: c = 10", "Answer: 10 cm"]}
{"text": "The capital of South Korea is Seoul.", "needs_thinking": false}
{"text": "If you have 20 red balls and 30 blue balls, what is the probability of picking a red ball?", "needs_thinking": true, "reasoning": ["Count total balls: 20 + 30 = 50", "Count favorable outcomes (red balls): 20", "Calculate probability: 20/50", "Simplify: 2/5", "Answer: 2/5 or 0.4"]}
{"text": "The largest ocean is the Pacific Ocean.", "needs_thinking": false}
{"text": "If a loan of $5000 has an annual interest rate of 6%, how much interest is accrued in 3 years?", "needs_thinking": true, "reasoning": ["Principal = $5000", "Rate = 6% = 0.06", "Time = 3 years", "Simple interest = Principal × Rate × Time", "Calculate: 5000 × 0.06 × 3 = $900", "Answer: $900"]}
{"text": "The capital of Argentina is Buenos Aires.", "needs_thinking": false}
{"text": "If a sequence is defined by an = n^2 - 1, what is the 4th term?", "needs_thinking": true, "reasoning": ["Substitute n = 4 into the formula: a4 = 4^2 - 1", "Calculate: 16 - 1 = 15", "Answer: 15"]}
{"text": "The chemical symbol for iron is Fe.", "needs_thinking": false}
{"text": "If a recipe calls for 1.5 cups of sugar for 18 cookies, how much sugar is needed for 6 cookies?", "needs_thinking": true, "reasoning": ["Find sugar per cookie: 1.5 cups / 18 cookies = 1/12 cup per cookie", "Multiply by desired cookies: (1/12) × 6 = 0.5", "Answer: 0.5 cups of sugar"]}
{"text": "The capital of Mexico is Mexico City.", "needs_thinking": false}
{"text": "If a rectangle has an area of 48 sq cm and a length of 8 cm, what is its width?", "needs_thinking": true, "reasoning": ["Area = length × width", "Substitute values: 48 = 8 × width", "Solve for width: 48 / 8 = 6", "Answer: 6 cm"]}
{"text": "The chemical symbol for carbon is C.", "needs_thinking": false}
{"text": "If you roll a standard six-sided die, what is the probability of rolling an even number?", "needs_thinking": true, "reasoning": ["Possible outcomes: 1, 2, 3, 4, 5, 6 (6 total outcomes)", "Favorable outcomes (even numbers): 2, 4, 6 (3 outcomes)", "Calculate probability: 3/6", "Simplify: 1/2", "Answer: 1/2 or 0.5"]}
{"text": "The capital of India is New Delhi.", "needs_thinking": false}
{"text": "If a car travels at 80 km/h for 2.5 hours, how far does it go?", "needs_thinking": true, "reasoning": ["Distance = speed × time", "Substitute values: speed = 80 km/h, time = 2.5 hours", "Calculate: 80 × 2.5 = 200", "Answer: 200 km"]}
{"text": "The chemical symbol for gold is Au.", "needs_thinking": false}
{"text": "If a sequence is 10, 7, 4, 1, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: each number decreases by 3", "Last number is 1", "Subtract 3 from 1", "Answer: -2"]}
{"text": "The capital of South Africa is Pretoria (executive), Bloemfontein (judicial), and Cape Town (legislative).", "needs_thinking": false}
{"text": "If a circle has a diameter of 14 cm, what is its area? (Use π ≈ 22/7)", "needs_thinking": true, "reasoning": ["Radius = diameter / 2 = 14 / 2 = 7 cm", "Formula for area: A = πr^2", "Substitute values: A = (22/7) × 7^2", "Calculate: A = (22/7) × 49 = 22 × 7 = 154", "Answer: 154 square cm"]}
{"text": "The chemical symbol for silver is Ag.", "needs_thinking": false}
{"text": "If you have $500 and spend 30% of it, how much money do you have left?", "needs_thinking": true, "reasoning": ["Calculate amount spent: 30% of $500 = 0.30 × 500 = $150", "Subtract spent from total: $500 - $150 = $350", "Answer: $350"]}
{"text": "The capital of Russia is Moscow.", "needs_thinking": false}
{"text": "If a recipe calls for 2 cups of milk for 16 pancakes, how much milk is needed for 8 pancakes?", "needs_thinking": true, "reasoning": ["Find milk per pancake: 2 cups / 16 pancakes = 1/8 cup per pancake", "Multiply by desired pancakes: (1/8) × 8 = 1", "Answer: 1 cup of milk"]}
{"text": "The chemical symbol for nitrogen is N.", "needs_thinking": false}
{"text": "If a square has an area of 81 sq cm, what is its perimeter?", "needs_thinking": true, "reasoning": ["Area = side^2", "81 = side^2", "Side = sqrt(81) = 9 cm", "Perimeter = 4 × side = 4 × 9 = 36", "Answer: 36 cm"]}
{"text": "The capital of China is Beijing.", "needs_thinking": false}
{"text": "If you roll two standard six-sided dice, what is the probability that their sum is 7?", "needs_thinking": true, "reasoning": ["Total possible outcomes: 6 × 6 = 36", "Favorable outcomes (sum is 7): (1,6), (2,5), (3,4), (4,3), (5,2), (6,1) (6 outcomes)", "Calculate probability: 6/36", "Simplify: 1/6", "Answer: 1/6"]}
{"text": "The chemical symbol for potassium is K.", "needs_thinking": false}
{"text": "If a car travels 300 miles in 5 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 300 miles, Time = 5 hours", "Calculate: 300 / 5 = 60", "Answer: 60 mph"]}
{"text": "The capital of Germany is Berlin.", "needs_thinking": false}
{"text": "If a sequence is defined by an = 3n - 2, what is the 6th term?", "needs_thinking": true, "reasoning": ["Substitute n = 6 into the formula: a6 = 3(6) - 2", "Calculate: 18 - 2 = 16", "Answer: 16"]}
{"text": "The chemical symbol for calcium is Ca.", "needs_thinking": false}
{"text": "If a recipe calls for 0.5 kg of sugar for 20 cookies, how much sugar is needed for 10 cookies?", "needs_thinking": true, "reasoning": ["Find sugar per cookie: 0.5 kg / 20 cookies = 0.025 kg per cookie", "Multiply by desired cookies: 0.025 × 10 = 0.25", "Answer: 0.25 kg of sugar"]}
{"text": "The capital of Japan is Tokyo.", "needs_thinking": false}
{"text": "If a rectangle has a perimeter of 30 cm and a length of 10 cm, what is its width?", "needs_thinking": true, "reasoning": ["Perimeter = 2 × (length + width)", "30 = 2 × (10 + width)", "Divide by 2: 15 = 10 + width", "Subtract 10: width = 5", "Answer: 5 cm"]}
{"text": "The chemical symbol for chlorine is Cl.", "needs_thinking": false}
{"text": "If you have 12 red roses and 8 white roses, what is the ratio of white roses to red roses?", "needs_thinking": true, "reasoning": ["Count white roses: 8", "Count red roses: 12", "Form the ratio: 8/12", "Simplify: 2/3", "Answer: 2:3 or 2/3"]}
{"text": "The capital of Italy is Rome.", "needs_thinking": false}
{"text": "If a store offers a 25% discount on an item priced at $120, what is the final price?", "needs_thinking": true, "reasoning": ["Calculate discount amount: 25% of $120 = 0.25 × 120 = $30", "Subtract discount from original price: $120 - $30 = $90", "Answer: $90"]}
{"text": "The chemical symbol for hydrogen is H.", "needs_thinking": false}
{"text": "If a sequence is 1, 2, 4, 7, 11, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: differences are increasing by 1 (1, 2, 3, 4)", "Next difference should be 5", "Add 5 to the last number (11)", "Answer: 16"]}
{"text": "The capital of the United Kingdom is London.", "needs_thinking": false}
{"text": "If a car travels 400 km in 5 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 400 km, Time = 5 hours", "Calculate: 400 / 5 = 80", "Answer: 80 km/h"]}
{"text": "The chemical symbol for oxygen is O.", "needs_thinking": false}
{"text": "If a recipe calls for 3 cups of flour for 18 cookies, how much flour is needed for 6 cookies?", "needs_thinking": true, "reasoning": ["Find flour per cookie: 3 cups / 18 cookies = 1/6 cup per cookie", "Multiply by desired cookies: (1/6) × 6 = 1", "Answer: 1 cup of flour"]}
{"text": "The capital of France is Paris.", "needs_thinking": false}
{"text": "If a square has a side length of 7 cm, what is its area and perimeter?", "needs_thinking": true, "reasoning": ["Area = side × side = 7 × 7 = 49 square cm", "Perimeter = 4 × side = 4 × 7 = 28 cm", "Answer: Area = 49 sq cm, Perimeter = 28 cm"]}
{"text": "The chemical symbol for nitrogen is N.", "needs_thinking": false}
{"text": "If you have 10 red apples and 15 green apples, what is the probability of picking a green apple?", "needs_thinking": true, "reasoning": ["Count total apples: 10 + 15 = 25", "Count favorable outcomes (green apples): 15", "Calculate probability: 15/25", "Simplify: 3/5", "Answer: 3/5 or 0.6"]}
{"text": "The capital of Germany is Berlin.", "needs_thinking": false}
{"text": "If a loan of $10000 has an annual interest rate of 4%, how much interest is accrued in 2 years?", "needs_thinking": true, "reasoning": ["Principal = $10000", "Rate = 4% = 0.04", "Time = 2 years", "Simple interest = Principal × Rate × Time", "Calculate: 10000 × 0.04 × 2 = $800", "Answer: $800"]}
{"text": "The chemical symbol for iron is Fe.", "needs_thinking": false}
{"text": "If a sequence is defined by an = 2^n, what is the 5th term?", "needs_thinking": true, "reasoning": ["Substitute n = 5 into the formula: a5 = 2^5", "Calculate: 32", "Answer: 32"]}
{"text": "The capital of Spain is Madrid.", "needs_thinking": false}
{"text": "If a recipe calls for 2.5 cups of water for 5 servings, how much water is needed for 10 servings?", "needs_thinking": true, "reasoning": ["Find water per serving: 2.5 cups / 5 servings = 0.5 cup per serving", "Multiply by desired servings: 0.5 × 10 = 5", "Answer: 5 cups of water"]}
{"text": "The chemical symbol for copper is Cu.", "needs_thinking": false}
{"text": "If a rectangle has an area of 60 sq cm and a width of 6 cm, what is its length?", "needs_thinking": true, "reasoning": ["Area = length × width", "Substitute values: 60 = length × 6", "Solve for length: 60 / 6 = 10", "Answer: 10 cm"]}
{"text": "The capital of Canada is Ottawa.", "needs_thinking": false}
{"text": "If you roll a standard six-sided die, what is the probability of rolling a number greater than 4?", "needs_thinking": true, "reasoning": ["Possible outcomes: 1, 2, 3, 4, 5, 6 (6 total outcomes)", "Favorable outcomes (greater than 4): 5, 6 (2 outcomes)", "Calculate probability: 2/6", "Simplify: 1/3", "Answer: 1/3 or approximately 0.333"]}
{"text": "The chemical symbol for zinc is Zn.", "needs_thinking": false}
{"text": "If a car travels 150 miles in 3 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 150 miles, Time = 3 hours", "Calculate: 150 / 3 = 50", "Answer: 50 mph"]}
{"text": "The capital of Australia is Canberra.", "needs_thinking": false}
{"text": "If a sequence is 1, 3, 6, 10, 15, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: differences are increasing by 1 (2, 3, 4, 5)", "Next difference should be 6", "Add 6 to the last number (15)", "Answer: 21"]}
{"text": "The chemical symbol for lead is Pb.", "needs_thinking": false}
{"text": "If a recipe calls for 4 eggs for 2 dozen cookies, how many eggs are needed for 3 dozen cookies?", "needs_thinking": true, "reasoning": ["Find eggs per dozen: 4 eggs / 2 dozen = 2 eggs per dozen", "Multiply by desired dozens: 2 × 3 = 6", "Answer: 6 eggs"]}
{"text": "The capital of Brazil is Brasília.", "needs_thinking": false}
{"text": "If a circle has a circumference of 88 cm, what is its radius? (Use π ≈ 22/7)", "needs_thinking": true, "reasoning": ["Formula for circumference: C = 2πr", "Substitute values: 88 = 2 × (22/7) × r", "Simplify: 88 = (44/7) × r", "Solve for r: r = 88 × (7/44) = 2 × 7 = 14", "Answer: 14 cm"]}
{"text": "The chemical symbol for sulfur is S.", "needs_thinking": false}
{"text": "If you have $1000 and spend 20% on rent and 15% on food, how much money do you have left?", "needs_thinking": true, "reasoning": ["Calculate rent spent: 20% of $1000 = 0.20 × 1000 = $200", "Calculate food spent: 15% of $1000 = 0.15 × 1000 = $150", "Total spent: $200 + $150 = $350", "Subtract total spent from total: $1000 - $350 = $650", "Answer: $650"]}
{"text": "The capital of Egypt is Cairo.", "needs_thinking": false}
{"text": "If a sequence is defined by an = n^3, what is the 3rd term?", "needs_thinking": true, "reasoning": ["Substitute n = 3 into the formula: a3 = 3^3", "Calculate: 27", "Answer: 27"]}
{"text": "The chemical symbol for phosphorus is P.", "needs_thinking": false}
{"text": "If a car travels 200 km in 2.5 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 200 km, Time = 2.5 hours", "Calculate: 200 / 2.5 = 80", "Answer: 80 km/h"]}
{"text": "The capital of South Korea is Seoul.", "needs_thinking": false}
{"text": "If a right-angled triangle has a hypotenuse of 13 cm and one leg of 5 cm, what is the length of the other leg?", "needs_thinking": true, "reasoning": ["Use Pythagorean theorem: a^2 + b^2 = c^2", "Substitute values: 5^2 + b^2 = 13^2", "Calculate: 25 + b^2 = 169", "Subtract 25: b^2 = 144", "Take square root: b = 12", "Answer: 12 cm"]}
{"text": "The chemical symbol for silicon is Si.", "needs_thinking": false}
{"text": "If you have 10 red socks and 10 blue socks in a drawer, what is the probability of picking two red socks in a row without replacement?", "needs_thinking": true, "reasoning": ["Total socks: 10 + 10 = 20", "Probability of first red sock: 10/20 = 1/2", "After picking one red, 9 red socks left and 19 total socks", "Probability of second red sock: 9/19", "Multiply probabilities: (1/2) × (9/19) = 9/38", "Answer: 9/38"]}
{"text": "The capital of Argentina is Buenos Aires.", "needs_thinking": false}
{"text": "If a loan of $2000 has an annual interest rate of 7%, how much interest is accrued in 4 years?", "needs_thinking": true, "reasoning": ["Principal = $2000", "Rate = 7% = 0.07", "Time = 4 years", "Simple interest = Principal × Rate × Time", "Calculate: 2000 × 0.07 × 4 = $560", "Answer: $560"]}
{"text": "The chemical symbol for aluminum is Al.", "needs_thinking": false}
{"text": "If a sequence is 1, 1/2, 1/4, 1/8, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: each number is half of the previous one", "Last number is 1/8", "Multiply 1/8 by 1/2", "Answer: 1/16"]}
{"text": "The capital of Mexico is Mexico City.", "needs_thinking": false}
{"text": "If a recipe calls for 300g of flour for 12 cookies, how much flour is needed for 36 cookies?", "needs_thinking": true, "reasoning": ["Find flour per cookie: 300g / 12 cookies = 25g per cookie", "Multiply by desired cookies: 25 × 36 = 900", "Answer: 900g of flour"]}
{"text": "The chemical symbol for sodium is Na.", "needs_thinking": false}
{"text": "If a rectangle has a length of 12 cm and a width of 7 cm, what is its area and perimeter?", "needs_thinking": true, "reasoning": ["Area = length × width = 12 × 7 = 84 square cm", "Perimeter = 2 × (length + width) = 2 × (12 + 7) = 2 × 19 = 38 cm", "Answer: Area = 84 sq cm, Perimeter = 38 cm"]}
{"text": "The capital of India is New Delhi.", "needs_thinking": false}
{"text": "If you roll a standard six-sided die, what is the probability of rolling a number less than 3?", "needs_thinking": true, "reasoning": ["Possible outcomes: 1, 2, 3, 4, 5, 6 (6 total outcomes)", "Favorable outcomes (less than 3): 1, 2 (2 outcomes)", "Calculate probability: 2/6", "Simplify: 1/3", "Answer: 1/3 or approximately 0.333"]}
{"text": "The chemical symbol for potassium is K.", "needs_thinking": false}
{"text": "If a car travels 100 miles in 2 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 100 miles, Time = 2 hours", "Calculate: 100 / 2 = 50", "Answer: 50 mph"]}
{"text": "The capital of South Africa is Pretoria (executive), Bloemfontein (judicial), and Cape Town (legislative).", "needs_thinking": false}
{"text": "If a sequence is defined by an = n^2 + n, what is the 4th term?", "needs_thinking": true, "reasoning": ["Substitute n = 4 into the formula: a4 = 4^2 + 4", "Calculate: 16 + 4 = 20", "Answer: 20"]}
{"text": "The chemical symbol for chlorine is Cl.", "needs_thinking": false}
{"text": "If a recipe calls for 1 cup of sugar for 12 cookies, how much sugar is needed for 36 cookies?", "needs_thinking": true, "reasoning": ["Find sugar per cookie: 1 cup / 12 cookies = 1/12 cup per cookie", "Multiply by desired cookies: (1/12) × 36 = 3", "Answer: 3 cups of sugar"]}
{"text": "The capital of Russia is Moscow.", "needs_thinking": false}
{"text": "If a square has a perimeter of 36 cm, what is its area?", "needs_thinking": true, "reasoning": ["Perimeter = 4 × side length", "36 = 4 × side length", "Solve for side: 36 / 4 = 9 cm", "Area = side × side = 9 × 9", "Answer: 81 square cm"]}
{"text": "The chemical symbol for hydrogen is H.", "needs_thinking": false}
{"text": "If you flip a fair coin four times, what is the probability of getting exactly three heads?", "needs_thinking": true, "reasoning": ["Total possible outcomes: 2^4 = 16", "Favorable outcomes (exactly three heads): HHHT, HHTH, HTHH, THHH (4 outcomes)", "Calculate probability: 4/16", "Simplify: 1/4", "Answer: 1/4"]}
{"text": "The capital of the United Kingdom is London.", "needs_thinking": false}
{"text": "If a car travels 250 km in 2.5 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 250 km, Time = 2.5 hours", "Calculate: 250 / 2.5 = 100", "Answer: 100 km/h"]}
{"text": "The chemical symbol for calcium is Ca.", "needs_thinking": false}
{"text": "If a recipe calls for 500g of chicken for 4 servings, how much chicken is needed for 6 servings?", "needs_thinking": true, "reasoning": ["Find chicken per serving: 500g / 4 servings = 125g per serving", "Multiply by desired servings: 125 × 6 = 750", "Answer: 750g of chicken"]}
{"text": "The capital of Japan is Tokyo.", "needs_thinking": false}
{"text": "If a right-angled triangle has legs of 5 cm and 12 cm, what is the length of the hypotenuse?", "needs_thinking": true, "reasoning": ["Use Pythagorean theorem: a^2 + b^2 = c^2", "Substitute legs: 5^2 + 12^2 = c^2", "Calculate: 25 + 144 = c^2", "169 = c^2", "Take square root: c = 13", "Answer: 13 cm"]}
{"text": "The chemical symbol for copper is Cu.", "needs_thinking": false}
{"text": "If you have 15 red marbles and 10 blue marbles, what is the ratio of blue marbles to total marbles?", "needs_thinking": true, "reasoning": ["Count blue marbles: 10", "Count total marbles: 15 + 10 = 25", "Form the ratio: 10/25", "Simplify: 2/5", "Answer: 2:5 or 2/5"]}
{"text": "The capital of Italy is Rome.", "needs_thinking": false}
{"text": "If a store offers a 10% discount on an item priced at $80, what is the final price?", "needs_thinking": true, "reasoning": ["Calculate discount amount: 10% of $80 = 0.10 × 80 = $8", "Subtract discount from original price: $80 - $8 = $72", "Answer: $72"]}
{"text": "The chemical symbol for zinc is Zn.", "needs_thinking": false}
{"text": "If a sequence is 2, 5, 10, 17, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: differences are increasing by 2 (3, 5, 7)", "Next difference should be 9", "Add 9 to the last number (17)", "Answer: 26"]}
{"text": "The capital of Canada is Ottawa.", "needs_thinking": false}
{"text": "If a car travels 120 miles in 2 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 120 miles, Time = 2 hours", "Calculate: 120 / 2 = 60", "Answer: 60 mph"]}
{"text": "The chemical symbol for lead is Pb.", "needs_thinking": false}
{"text": "If a recipe calls for 2 cups of sugar for 24 cookies, how much sugar is needed for 12 cookies?", "needs_thinking": true, "reasoning": ["Find sugar per cookie: 2 cups / 24 cookies = 1/12 cup per cookie", "Multiply by desired cookies: (1/12) × 12 = 1", "Answer: 1 cup of sugar"]}
{"text": "The capital of Australia is Canberra.", "needs_thinking": false}
{"text": "If a circle has a radius of 10 cm, what is its area? (Use π ≈ 3.14)", "needs_thinking": true, "reasoning": ["Formula for area: A = πr^2", "Substitute values: A = 3.14 × 10^2", "Calculate: A = 3.14 × 100 = 314", "Answer: 314 square cm"]}
{"text": "The chemical symbol for sulfur is S.", "needs_thinking": false}
{"text": "If you have $200 and spend 25% on clothes and 10% on entertainment, how much money do you have left?", "needs_thinking": true, "reasoning": ["Calculate clothes spent: 25% of $200 = 0.25 × 200 = $50", "Calculate entertainment spent: 10% of $200 = 0.10 × 200 = $20", "Total spent: $50 + $20 = $70", "Subtract total spent from total: $200 - $70 = $130", "Answer: $130"]}
{"text": "The capital of Brazil is Brasília.", "needs_thinking": false}
{"text": "If a sequence is defined by an = 2n^2, what is the 3rd term?", "needs_thinking": true, "reasoning": ["Substitute n = 3 into the formula: a3 = 2 × 3^2", "Calculate: 2 × 9 = 18", "Answer: 18"]}
{"text": "The chemical symbol for phosphorus is P.", "needs_thinking": false}
{"text": "If a car travels 360 km in 4 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 360 km, Time = 4 hours", "Calculate: 360 / 4 = 90", "Answer: 90 km/h"]}
{"text": "The capital of Egypt is Cairo.", "needs_thinking": false}
{"text": "If a right-angled triangle has a hypotenuse of 10 cm and one leg of 6 cm, what is the length of the other leg?", "needs_thinking": true, "reasoning": ["Use Pythagorean theorem: a^2 + b^2 = c^2", "Substitute values: 6^2 + b^2 = 10^2", "Calculate: 36 + b^2 = 100", "Subtract 36: b^2 = 64", "Take square root: b = 8", "Answer: 8 cm"]}
{"text": "The chemical symbol for silicon is Si.", "needs_thinking": false}
{"text": "If you have 8 red socks and 12 blue socks in a drawer, what is the probability of picking two blue socks in a row without replacement?", "needs_thinking": true, "reasoning": ["Total socks: 8 + 12 = 20", "Probability of first blue sock: 12/20 = 3/5", "After picking one blue, 11 blue socks left and 19 total socks", "Probability of second blue sock: 11/19", "Multiply probabilities: (3/5) × (11/19) = 33/95", "Answer: 33/95"]}
{"text": "The capital of South Korea is Seoul.", "needs_thinking": false}
{"text": "If a loan of $1000 has an annual interest rate of 5%, how much interest is accrued in 5 years?", "needs_thinking": true, "reasoning": ["Principal = $1000", "Rate = 5% = 0.05", "Time = 5 years", "Simple interest = Principal × Rate × Time", "Calculate: 1000 × 0.05 × 5 = $250", "Answer: $250"]}
{"text": "The chemical symbol for aluminum is Al.", "needs_thinking": false}
{"text": "If a sequence is 1, 3, 9, 27, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: each number is multiplied by 3", "Last number is 27", "Multiply 27 by 3", "Answer: 81"]}
{"text": "The capital of Argentina is Buenos Aires.", "needs_thinking": false}
{"text": "If a recipe calls for 250g of butter for 20 cookies, how much butter is needed for 10 cookies?", "needs_thinking": true, "reasoning": ["Find butter per cookie: 250g / 20 cookies = 12.5g per cookie", "Multiply by desired cookies: 12.5 × 10 = 125", "Answer: 125g of butter"]}
{"text": "The chemical symbol for sodium is Na.", "needs_thinking": false}
{"text": "If a rectangle has a length of 15 cm and a width of 8 cm, what is its area and perimeter?", "needs_thinking": true, "reasoning": ["Area = length × width = 15 × 8 = 120 square cm", "Perimeter = 2 × (length + width) = 2 × (15 + 8) = 2 × 23 = 46 cm", "Answer: Area = 120 sq cm, Perimeter = 46 cm"]}
{"text": "The capital of Mexico is Mexico City.", "needs_thinking": false}
{"text": "If you roll a standard six-sided die, what is the probability of rolling an odd number?", "needs_thinking": true, "reasoning": ["Possible outcomes: 1, 2, 3, 4, 5, 6 (6 total outcomes)", "Favorable outcomes (odd numbers): 1, 3, 5 (3 outcomes)", "Calculate probability: 3/6", "Simplify: 1/2", "Answer: 1/2 or 0.5"]}
{"text": "The chemical symbol for potassium is K.", "needs_thinking": false}
{"text": "If a car travels 180 km in 3 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 180 km, Time = 3 hours", "Calculate: 180 / 3 = 60", "Answer: 60 km/h"]}
{"text": "The capital of India is New Delhi.", "needs_thinking": false}
{"text": "If a sequence is defined by an = n^2 - 2n, what is the 5th term?", "needs_thinking": true, "reasoning": ["Substitute n = 5 into the formula: a5 = 5^2 - 2(5)", "Calculate: 25 - 10 = 15", "Answer: 15"]}
{"text": "The chemical symbol for chlorine is Cl.", "needs_thinking": false}
{"text": "If a recipe calls for 3 eggs for 12 cupcakes, how many eggs are needed for 36 cupcakes?", "needs_thinking": true, "reasoning": ["Find eggs per cupcake: 3 eggs / 12 cupcakes = 1/4 egg per cupcake", "Multiply by desired cupcakes: (1/4) × 36 = 9", "Answer: 9 eggs"]}
{"text": "The capital of Russia is Moscow.", "needs_thinking": false}
{"text": "If a square has an area of 100 sq cm, what is its perimeter?", "needs_thinking": true, "reasoning": ["Area = side^2", "100 = side^2", "Side = sqrt(100) = 10 cm", "Perimeter = 4 × side = 4 × 10 = 40", "Answer: 40 cm"]}
{"text": "The chemical symbol for hydrogen is H.", "needs_thinking": false}
{"text": "If you flip a fair coin five times, what is the probability of getting exactly four heads?", "needs_thinking": true, "reasoning": ["Total possible outcomes: 2^5 = 32", "Favorable outcomes (exactly four heads): HHHHT, HHHTH, HHTHH, HTHHH, THHHH (5 outcomes)", "Calculate probability: 5/32", "Answer: 5/32"]}
{"text": "The capital of the United Kingdom is London.", "needs_thinking": false}
{"text": "If a car travels 300 km in 3 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 300 km, Time = 3 hours", "Calculate: 300 / 3 = 100", "Answer: 100 km/h"]}
{"text": "The chemical symbol for calcium is Ca.", "needs_thinking": false}
{"text": "If a recipe calls for 600g of flour for 2 loaves of bread, how much flour is needed for 3 loaves?", "needs_thinking": true, "reasoning": ["Find flour per loaf: 600g / 2 loaves = 300g per loaf", "Multiply by desired loaves: 300 × 3 = 900", "Answer: 900g of flour"]}
{"text": "The capital of Japan is Tokyo.", "needs_thinking": false}
{"text": "If a right-angled triangle has a hypotenuse of 17 cm and one leg of 8 cm, what is the length of the other leg?", "needs_thinking": true, "reasoning": ["Use Pythagorean theorem: a^2 + b^2 = c^2", "Substitute values: 8^2 + b^2 = 17^2", "Calculate: 64 + b^2 = 289", "Subtract 64: b^2 = 225", "Take square root: b = 15", "Answer: 15 cm"]}
{"text": "The chemical symbol for copper is Cu.", "needs_thinking": false}
{"text": "If you have 25 red marbles and 25 blue marbles, what is the probability of picking a red marble?", "needs_thinking": true, "reasoning": ["Count total marbles: 25 + 25 = 50", "Count favorable outcomes (red marbles): 25", "Calculate probability: 25/50", "Simplify: 1/2", "Answer: 1/2 or 0.5"]}
{"text": "The capital of Italy is Rome.", "needs_thinking": false}
{"text": "If a store offers a 30% discount on an item priced at $90, what is the final price?", "needs_thinking": true, "reasoning": ["Calculate discount amount: 30% of $90 = 0.30 × 90 = $27", "Subtract discount from original price: $90 - $27 = $63", "Answer: $63"]}
{"text": "The chemical symbol for zinc is Zn.", "needs_thinking": false}
{"text": "If a sequence is 3, 6, 12, 24, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: each number is doubled", "Last number is 24", "Multiply 24 by 2", "Answer: 48"]}
{"text": "The capital of Canada is Ottawa.", "needs_thinking": false}
{"text": "If a car travels 200 miles in 4 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 200 miles, Time = 4 hours", "Calculate: 200 / 4 = 50", "Answer: 50 mph"]}
{"text": "The chemical symbol for lead is Pb.", "needs_thinking": false}
{"text": "If a recipe calls for 1.5 cups of milk for 12 muffins, how much milk is needed for 6 muffins?", "needs_thinking": true, "reasoning": ["Find milk per muffin: 1.5 cups / 12 muffins = 0.125 cup per muffin", "Multiply by desired muffins: 0.125 × 6 = 0.75", "Answer: 0.75 cups of milk"]}
{"text": "The capital of Australia is Canberra.", "needs_thinking": false}
{"text": "If a circle has a circumference of 62.8 cm, what is its radius? (Use π ≈ 3.14)", "needs_thinking": true, "reasoning": ["Formula for circumference: C = 2πr", "Substitute values: 62.8 = 2 × 3.14 × r", "Simplify: 62.8 = 6.28 × r", "Solve for r: r = 62.8 / 6.28 = 10", "Answer: 10 cm"]}
{"text": "The chemical symbol for sulfur is S.", "needs_thinking": false}
{"text": "If you have $750 and spend 40% on rent and 20% on groceries, how much money do you have left?", "needs_thinking": true, "reasoning": ["Calculate rent spent: 40% of $750 = 0.40 × 750 = $300", "Calculate groceries spent: 20% of $750 = 0.20 × 750 = $150", "Total spent: $300 + $150 = $450", "Subtract total spent from total: $750 - $450 = $300", "Answer: $300"]}
{"text": "The capital of Brazil is Brasília.", "needs_thinking": false}
{"text": "If a sequence is defined by an = n^3 - n, what is the 4th term?", "needs_thinking": true, "reasoning": ["Substitute n = 4 into the formula: a4 = 4^3 - 4", "Calculate: 64 - 4 = 60", "Answer: 60"]}
{"text": "The chemical symbol for phosphorus is P.", "needs_thinking": false}
{"text": "If a car travels 450 km in 5 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 450 km, Time = 5 hours", "Calculate: 450 / 5 = 90", "Answer: 90 km/h"]}
{"text": "The capital of Egypt is Cairo.", "needs_thinking": false}
{"text": "If a right-angled triangle has a hypotenuse of 25 cm and one leg of 7 cm, what is the length of the other leg?", "needs_thinking": true, "reasoning": ["Use Pythagorean theorem: a^2 + b^2 = c^2", "Substitute values: 7^2 + b^2 = 25^2", "Calculate: 49 + b^2 = 625", "Subtract 49: b^2 = 576", "Take square root: b = 24", "Answer: 24 cm"]}
{"text": "The chemical symbol for silicon is Si.", "needs_thinking": false}
{"text": "If you have 6 red socks and 9 blue socks in a drawer, what is the probability of picking two red socks in a row without replacement?", "needs_thinking": true, "reasoning": ["Total socks: 6 + 9 = 15", "Probability of first red sock: 6/15 = 2/5", "After picking one red, 5 red socks left and 14 total socks", "Probability of second red sock: 5/14", "Multiply probabilities: (2/5) × (5/14) = 10/70 = 1/7", "Answer: 1/7"]}
{"text": "The capital of South Korea is Seoul.", "needs_thinking": false}
{"text": "If a loan of $3000 has an annual interest rate of 8%, how much interest is accrued in 2 years?", "needs_thinking": true, "reasoning": ["Principal = $3000", "Rate = 8% = 0.08", "Time = 2 years", "Simple interest = Principal × Rate × Time", "Calculate: 3000 × 0.08 × 2 = $480", "Answer: $480"]}
{"text": "The chemical symbol for aluminum is Al.", "needs_thinking": false}
{"text": "If a sequence is 2, 6, 18, 54, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: each number is multiplied by 3", "Last number is 54", "Multiply 54 by 3", "Answer: 162"]}
{"text": "The capital of Argentina is Buenos Aires.", "needs_thinking": false}
{"text": "If a recipe calls for 400g of sugar for 16 cookies, how much sugar is needed for 8 cookies?", "needs_thinking": true, "reasoning": ["Find sugar per cookie: 400g / 16 cookies = 25g per cookie", "Multiply by desired cookies: 25 × 8 = 200", "Answer: 200g of sugar"]}
{"text": "The chemical symbol for sodium is Na.", "needs_thinking": false}
{"text": "If a rectangle has a length of 20 cm and a width of 10 cm, what is its area and perimeter?", "needs_thinking": true, "reasoning": ["Area = length × width = 20 × 10 = 200 square cm", "Perimeter = 2 × (length + width) = 2 × (20 + 10) = 2 × 30 = 60 cm", "Answer: Area = 200 sq cm, Perimeter = 60 cm"]}
{"text": "The capital of Mexico is Mexico City.", "needs_thinking": false}
{"text": "If you roll a standard six-sided die, what is the probability of rolling a number greater than 2?", "needs_thinking": true, "reasoning": ["Possible outcomes: 1, 2, 3, 4, 5, 6 (6 total outcomes)", "Favorable outcomes (greater than 2): 3, 4, 5, 6 (4 outcomes)", "Calculate probability: 4/6", "Simplify: 2/3", "Answer: 2/3 or approximately 0.667"]}
{"text": "The chemical symbol for potassium is K.", "needs_thinking": false}
{"text": "If a car travels 220 miles in 4 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 220 miles, Time = 4 hours", "Calculate: 220 / 4 = 55", "Answer: 55 mph"]}
{"text": "The capital of India is New Delhi.", "needs_thinking": false}
{"text": "If a sequence is defined by an = 2n + 5, what is the 7th term?", "needs_thinking": true, "reasoning": ["Substitute n = 7 into the formula: a7 = 2(7) + 5", "Calculate: 14 + 5 = 19", "Answer: 19"]}
{"text": "The chemical symbol for chlorine is Cl.", "needs_thinking": false}
{"text": "If a recipe calls for 2 eggs for 16 cookies, how many eggs are needed for 8 cookies?", "needs_thinking": true, "reasoning": ["Find eggs per cookie: 2 eggs / 16 cookies = 1/8 egg per cookie", "Multiply by desired cookies: (1/8) × 8 = 1", "Answer: 1 egg"]}
{"text": "The capital of Russia is Moscow.", "needs_thinking": false}
{"text": "If a square has a perimeter of 40 cm, what is its area?", "needs_thinking": true, "reasoning": ["Perimeter = 4 × side length", "40 = 4 × side length", "Solve for side: 40 / 4 = 10 cm", "Area = side × side = 10 × 10", "Answer: 100 square cm"]}
{"text": "The chemical symbol for hydrogen is H.", "needs_thinking": false}
{"text": "If you flip a fair coin six times, what is the probability of getting exactly three heads?", "needs_thinking": true, "reasoning": ["Total possible outcomes: 2^6 = 64", "Number of ways to get 3 heads in 6 flips (combinations): C(6,3) = 6! / (3! * (6-3)!) = (6*5*4)/(3*2*1) = 20", "Calculate probability: 20/64", "Simplify: 5/16", "Answer: 5/16"]}
{"text": "The capital of the United Kingdom is London.", "needs_thinking": false}
{"text": "If a car travels 350 km in 3.5 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 350 km, Time = 3.5 hours", "Calculate: 350 / 3.5 = 100", "Answer: 100 km/h"]}
{"text": "The chemical symbol for calcium is Ca.", "needs_thinking": false}
{"text": "If a recipe calls for 750g of beef for 6 servings, how much beef is needed for 4 servings?", "needs_thinking": true, "reasoning": ["Find beef per serving: 750g / 6 servings = 125g per serving", "Multiply by desired servings: 125 × 4 = 500", "Answer: 500g of beef"]}
{"text": "The capital of Japan is Tokyo.", "needs_thinking": false}
{"text": "If a right-angled triangle has a hypotenuse of 20 cm and one leg of 12 cm, what is the length of the other leg?", "needs_thinking": true, "reasoning": ["Use Pythagorean theorem: a^2 + b^2 = c^2", "Substitute values: 12^2 + b^2 = 20^2", "Calculate: 144 + b^2 = 400", "Subtract 144: b^2 = 256", "Take square root: b = 16", "Answer: 16 cm"]}
{"text": "The chemical symbol for copper is Cu.", "needs_thinking": false}
{"text": "If you have 30 red marbles and 20 blue marbles, what is the probability of picking a blue marble?", "needs_thinking": true, "reasoning": ["Count total marbles: 30 + 20 = 50", "Count favorable outcomes (blue marbles): 20", "Calculate probability: 20/50", "Simplify: 2/5", "Answer: 2/5 or 0.4"]}
{"text": "The capital of Italy is Rome.", "needs_thinking": false}
{"text": "If a store offers a 15% discount on an item priced at $60, what is the final price?", "needs_thinking": true, "reasoning": ["Calculate discount amount: 15% of $60 = 0.15 × 60 = $9", "Subtract discount from original price: $60 - $9 = $51", "Answer: $51"]}
{"text": "The chemical symbol for zinc is Zn.", "needs_thinking": false}
{"text": "If a sequence is 5, 10, 15, 20, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: each number increases by 5", "Last number is 20", "Add 5 to 20", "Answer: 25"]}
{"text": "The capital of Canada is Ottawa.", "needs_thinking": false}
{"text": "If a car travels 240 miles in 3 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 240 miles, Time = 3 hours", "Calculate: 240 / 3 = 80", "Answer: 80 mph"]}
{"text": "The chemical symbol for lead is Pb.", "needs_thinking": false}
{"text": "If a recipe calls for 2.5 kg of potatoes for 10 servings, how much potatoes are needed for 4 servings?", "needs_thinking": true, "reasoning": ["Find potatoes per serving: 2.5 kg / 10 servings = 0.25 kg per serving", "Multiply by desired servings: 0.25 × 4 = 1", "Answer: 1 kg of potatoes"]}
{"text": "The capital of Australia is Canberra.", "needs_thinking": false}
{"text": "If a circle has a radius of 14 cm, what is its circumference? (Use π ≈ 22/7)", "needs_thinking": true, "reasoning": ["Formula for circumference: C = 2πr", "Substitute values: C = 2 × (22/7) × 14", "Calculate: C = 2 × 22 × 2 = 88", "Answer: 88 cm"]}
{"text": "The chemical symbol for sulfur is S.", "needs_thinking": false}
{"text": "If you have $1200 and spend 30% on rent and 25% on food, how much money do you have left?", "needs_thinking": true, "reasoning": ["Calculate rent spent: 30% of $1200 = 0.30 × 1200 = $360", "Calculate food spent: 25% of $1200 = 0.25 × 1200 = $300", "Total spent: $360 + $300 = $660", "Subtract total spent from total: $1200 - $660 = $540", "Answer: $540"]}
{"text": "The capital of Brazil is Brasília.", "needs_thinking": false}
{"text": "If a sequence is defined by an = n^2 + 2n + 1, what is the 3rd term?", "needs_thinking": true, "reasoning": ["Substitute n = 3 into the formula: a3 = 3^2 + 2(3) + 1", "Calculate: 9 + 6 + 1 = 16", "Answer: 16"]}
{"text": "The chemical symbol for phosphorus is P.", "needs_thinking": false}
{"text": "If a car travels 500 km in 5 hours, what is its average speed?", "needs_thinking": true, "reasoning": ["Speed = Distance / Time", "Substitute values: Distance = 500 km, Time = 5 hours", "Calculate: 500 / 5 = 100", "Answer: 100 km/h"]}
{"text": "The capital of Egypt is Cairo.", "needs_thinking": false}
{"text": "If a right-angled triangle has a hypotenuse of 15 cm and one leg of 9 cm, what is the length of the other leg?", "needs_thinking": true, "reasoning": ["Use Pythagorean theorem: a^2 + b^2 = c^2", "Substitute values: 9^2 + b^2 = 15^2", "Calculate: 81 + b^2 = 225", "Subtract 81: b^2 = 144", "Take square root: b = 12", "Answer: 12 cm"]}
{"text": "The chemical symbol for silicon is Si.", "needs_thinking": false}
{"text": "If you have 7 red socks and 13 blue socks in a drawer, what is the probability of picking two blue socks in a row without replacement?", "needs_thinking": true, "reasoning": ["Total socks: 7 + 13 = 20", "Probability of first blue sock: 13/20", "After picking one blue, 12 blue socks left and 19 total socks", "Probability of second blue sock: 12/19", "Multiply probabilities: (13/20) × (12/19) = 156/380 = 39/95", "Answer: 39/95"]}
{"text": "The capital of South Korea is Seoul.", "needs_thinking": false}
{"text": "If a loan of $4000 has an annual interest rate of 6%, how much interest is accrued in 3 years?", "needs_thinking": true, "reasoning": ["Principal = $4000", "Rate = 6% = 0.06", "Time = 3 years", "Simple interest = Principal × Rate × Time", "Calculate: 4000 × 0.06 × 3 = $720", "Answer: $720"]}
{"text": "The chemical symbol for aluminum is Al.", "needs_thinking": false}
{"text": "If a sequence is 100, 50, 25, 12.5, what is the next number?", "needs_thinking": true, "reasoning": ["Observe the pattern: each number is half of the previous one", "Last number is 12.5", "Multiply 12.5 by 1/2", "Answer: 6.25"]}
{"text": "The capital of Argentina is Buenos Aires.", "needs_thinking": false}
{"text": "If a recipe calls for 500g of flour for 20 cookies, how much flour is needed for 30 cookies?", "needs_thinking": true, "reasoning": ["Find flour per cookie: 500g / 20 cookies = 25g per cookie", "Multiply by desired cookies: 25 × 30 = 750", "Answer: 750g of flour"]}
