"""
ThinkerModule - Non-autoregressive reasoning component.
Processes entire sequences at once to perform deeper reasoning and generate
visible reasoning steps along with hidden states for the main LLM.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple, Dict, Any, List
import math


class NonAutoregressiveAttention(nn.Module):
    """
    Non-autoregressive multi-head attention that processes entire sequences simultaneously.
    Unlike the causal attention in the main LLM, this allows bidirectional context.
    """

    def __init__(self, hidden_size: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        assert hidden_size % num_heads == 0

        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.head_dim = hidden_size // num_heads
        self.scale = 1.0 / math.sqrt(self.head_dim)

        self.q_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.k_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.v_proj = nn.Linear(hidden_size, hidden_size, bias=False)
        self.out_proj = nn.Linear(hidden_size, hidden_size)

        self.dropout = nn.Dropout(dropout)

    def forward(self, hidden_states: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass with bidirectional attention."""
        batch_size, seq_len, _ = hidden_states.shape

        # Project to Q, K, V
        q = self.q_proj(hidden_states)
        k = self.k_proj(hidden_states)
        v = self.v_proj(hidden_states)

        # Reshape for multi-head attention
        q = q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)

        # Scaled dot-product attention (bidirectional)
        attn_weights = torch.matmul(q, k.transpose(-2, -1)) * self.scale

        # Apply attention mask if provided
        if attention_mask is not None:
            # Convert attention mask to additive mask for attention weights
            # attention_mask: [batch_size, seq_len] -> [batch_size, 1, 1, seq_len]
            mask = (1.0 - attention_mask.unsqueeze(1).unsqueeze(1)) * -10000.0
            attn_weights = attn_weights + mask

        attn_weights = F.softmax(attn_weights, dim=-1)
        attn_weights = self.dropout(attn_weights)

        # Apply attention to values
        attn_output = torch.matmul(attn_weights, v)
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.hidden_size
        )

        return self.out_proj(attn_output)


class ReasoningBlock(nn.Module):
    """
    Reasoning block for the ThinkerModule.
    Combines bidirectional attention with enhanced feed-forward processing.
    """

    def __init__(self, hidden_size: int, num_heads: int, intermediate_size: int,
                 dropout: float = 0.1, layer_norm_eps: float = 1e-5):
        super().__init__()

        self.attention = NonAutoregressiveAttention(hidden_size, num_heads, dropout)

        # Enhanced feed-forward with gating mechanism
        self.feed_forward = nn.Sequential(
            nn.Linear(hidden_size, intermediate_size),
            nn.GELU(),
            nn.Linear(intermediate_size, hidden_size),
            nn.Dropout(dropout)
        )

        # Gating mechanism for reasoning control
        self.gate = nn.Sequential(
            nn.Linear(hidden_size, hidden_size),
            nn.Sigmoid()
        )

        self.ln1 = nn.LayerNorm(hidden_size, eps=layer_norm_eps)
        self.ln2 = nn.LayerNorm(hidden_size, eps=layer_norm_eps)
        self.ln3 = nn.LayerNorm(hidden_size, eps=layer_norm_eps)
        self.dropout = nn.Dropout(dropout)

    def forward(self, hidden_states: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass with gated reasoning."""
        # Self-attention with residual connection
        residual = hidden_states
        hidden_states = self.ln1(hidden_states)
        hidden_states = self.attention(hidden_states, attention_mask)
        hidden_states = self.dropout(hidden_states) + residual

        # Gated feed-forward processing
        residual = hidden_states
        normalized = self.ln2(hidden_states)
        ff_output = self.feed_forward(normalized)

        # Apply gating mechanism
        gate_values = self.gate(self.ln3(hidden_states))
        hidden_states = residual + gate_values * ff_output

        return hidden_states


class DynamicReasoningGenerator(nn.Module):
    """
    Generates dynamic reasoning steps with learned stopping mechanism.
    The model decides when to stop generating reasoning steps.
    """

    def __init__(self, hidden_size: int, vocab_size: int, max_steps: int = 16):
        super().__init__()
        self.hidden_size = hidden_size
        self.vocab_size = vocab_size
        self.max_steps = max_steps  # Safety limit, not fixed generation

        # Reasoning state tracker
        self.reasoning_state = nn.GRUCell(hidden_size, hidden_size)

        # Step decoder for generating reasoning text
        self.step_decoder = nn.Sequential(
            nn.Linear(hidden_size, hidden_size * 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size * 2, vocab_size)
        )

        # Stop decision network
        self.stop_predictor = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_size // 2, 1),
            nn.Sigmoid()
        )

        # Context attention for focusing on relevant input parts
        self.context_attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=0.1,
            batch_first=True
        )

        # Special tokens
        self.step_start_token = vocab_size  # Will be handled by tokenizer
        self.step_end_token = vocab_size + 1

    def forward(self, input_states: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                max_new_tokens_per_step: int = 20,
                stop_threshold: float = 0.7) -> Dict[str, Any]:
        """
        Generate dynamic reasoning steps.

        Args:
            input_states: Input hidden states [batch_size, seq_len, hidden_size]
            attention_mask: Attention mask [batch_size, seq_len]
            max_new_tokens_per_step: Maximum tokens per reasoning step
            stop_threshold: Threshold for stopping decision

        Returns:
            Dictionary containing reasoning steps and metadata
        """
        batch_size, seq_len, _ = input_states.shape
        device = input_states.device

        # Initialize reasoning state with pooled input
        if attention_mask is not None:
            mask_expanded = attention_mask.unsqueeze(-1).expand_as(input_states)
            masked_states = input_states * mask_expanded
            seq_lengths = attention_mask.sum(dim=1, keepdim=True).float()
            initial_state = masked_states.sum(dim=1) / seq_lengths
        else:
            initial_state = input_states.mean(dim=1)

        reasoning_state = initial_state  # [batch_size, hidden_size]

        reasoning_steps = []
        step_attentions = []
        stop_probabilities = []

        for step in range(self.max_steps):
            # Apply context attention to focus on relevant input parts
            state_query = reasoning_state.unsqueeze(1)  # [batch_size, 1, hidden_size]

            attended_context, attention_weights = self.context_attention(
                query=state_query,
                key=input_states,
                value=input_states,
                key_padding_mask=(attention_mask == 0) if attention_mask is not None else None
            )

            # Update reasoning state with attended context
            reasoning_state = self.reasoning_state(
                attended_context.squeeze(1),  # [batch_size, hidden_size]
                reasoning_state
            )

            # Generate step tokens
            step_logits = self.step_decoder(reasoning_state.unsqueeze(1))  # [batch_size, 1, vocab_size]

            # Decide whether to stop
            stop_prob = self.stop_predictor(reasoning_state).squeeze(-1)  # [batch_size]

            reasoning_steps.append(step_logits)
            step_attentions.append(attention_weights)
            stop_probabilities.append(stop_prob)

            # Check if we should stop (for all samples in batch)
            if (stop_prob > stop_threshold).all():
                break

        return {
            'reasoning_steps': reasoning_steps,
            'step_attentions': step_attentions,
            'stop_probabilities': stop_probabilities,
            'final_reasoning_state': reasoning_state,
            'num_steps_generated': len(reasoning_steps)
        }


class ThinkerModule(nn.Module):
    """
    Non-autoregressive ThinkerModule for enhanced reasoning.

    This module:
    1. Processes entire input sequences simultaneously (non-autoregressive)
    2. Performs deeper reasoning through multiple reasoning blocks
    3. Generates visible reasoning steps for user transparency
    4. Produces enhanced hidden states for the main LLM
    """

    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config

        # Model dimensions
        self.hidden_size = config['hidden_size']
        self.num_layers = config['num_layers']
        self.num_heads = config['num_attention_heads']
        self.max_reasoning_steps = config['max_reasoning_steps']

        # Input projection (from LLM hidden size to Thinker hidden size)
        self.input_projection = nn.Linear(
            config.get('input_hidden_size', self.hidden_size),
            self.hidden_size
        )

        # Reasoning blocks
        self.reasoning_blocks = nn.ModuleList([
            ReasoningBlock(
                hidden_size=self.hidden_size,
                num_heads=self.num_heads,
                intermediate_size=config['intermediate_size'],
                dropout=config['dropout'],
                layer_norm_eps=config['layer_norm_eps']
            ) for _ in range(self.num_layers)
        ])

        # Dynamic reasoning generator
        self.reasoning_generator = DynamicReasoningGenerator(
            hidden_size=self.hidden_size,
            vocab_size=config.get('vocab_size', 50257),
            max_steps=self.max_reasoning_steps
        )

        # Final layer norm
        self.ln_f = nn.LayerNorm(self.hidden_size, eps=config['layer_norm_eps'])

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights."""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)

    def forward(self, input_hidden_states: torch.Tensor,
                attention_mask: Optional[torch.Tensor] = None,
                generate_steps: bool = True,
                stop_threshold: Optional[float] = None) -> Dict[str, Any]:
        """
        Forward pass of the ThinkerModule.

        Args:
            input_hidden_states: Hidden states from input processing [batch_size, seq_len, input_hidden_size]
            attention_mask: Attention mask [batch_size, seq_len]
            generate_steps: Whether to generate visible reasoning steps
            stop_threshold: Threshold for stopping reasoning generation

        Returns:
            Dictionary containing:
            - enhanced_hidden_states: Enhanced hidden states for main LLM
            - reasoning_steps: List of reasoning step logits (if generate_steps=True)
            - step_attentions: Attention weights for each step
            - num_reasoning_steps: Number of steps actually generated
            - stop_probabilities: Stopping probabilities for each step
        """
        # Project input to thinker hidden size
        hidden_states = self.input_projection(input_hidden_states)

        # Apply reasoning blocks
        for block in self.reasoning_blocks:
            hidden_states = block(hidden_states, attention_mask)

        # Final layer norm
        enhanced_hidden_states = self.ln_f(hidden_states)

        result = {
            'enhanced_hidden_states': enhanced_hidden_states
        }

        # Generate dynamic reasoning steps if requested
        if generate_steps:
            # Use config stop_threshold if not provided
            if stop_threshold is None:
                stop_threshold = self.config.get('stop_threshold', 0.7)

            reasoning_output = self.reasoning_generator(
                input_states=enhanced_hidden_states,
                attention_mask=attention_mask,
                stop_threshold=stop_threshold
            )

            result.update({
                'reasoning_steps': reasoning_output['reasoning_steps'],
                'step_attentions': reasoning_output['step_attentions'],
                'num_reasoning_steps': reasoning_output['num_steps_generated'],
                'stop_probabilities': reasoning_output['stop_probabilities'],
                'final_reasoning_state': reasoning_output['final_reasoning_state']
            })

        return result

    def generate_reasoning_text(self, reasoning_steps: List[torch.Tensor],
                               tokenizer, max_tokens_per_step: int = 20,
                               stop_probabilities: Optional[List[torch.Tensor]] = None) -> List[str]:
        """
        Convert reasoning step logits to human-readable text.

        Args:
            reasoning_steps: List of step logits from forward pass
            tokenizer: Tokenizer for decoding
            max_tokens_per_step: Maximum tokens per reasoning step
            stop_probabilities: Optional stopping probabilities for each step

        Returns:
            List of reasoning step texts with stopping information
        """
        reasoning_texts = []

        for i, step_logits in enumerate(reasoning_steps):
            # Get top tokens for this step
            top_tokens = torch.topk(step_logits.squeeze(1), k=max_tokens_per_step, dim=-1)
            token_ids = top_tokens.indices[0]  # Take first batch item

            # Decode to text
            step_text = tokenizer.decode(token_ids, skip_special_tokens=True)

            # Add stopping probability if available
            if stop_probabilities and i < len(stop_probabilities):
                stop_prob = stop_probabilities[i][0].item()  # Take first batch item
                reasoning_texts.append(f"Step {i+1} (stop_prob: {stop_prob:.3f}): {step_text}")
            else:
                reasoning_texts.append(f"Step {i+1}: {step_text}")

        return reasoning_texts
