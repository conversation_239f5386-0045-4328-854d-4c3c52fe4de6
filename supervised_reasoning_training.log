2025-05-29 14:05:20,986 - __main__ - INFO - Starting supervised reasoning training with all improvements...
2025-05-29 14:05:20,986 - __main__ - INFO - Supervised reasoning configuration created
2025-05-29 14:05:20,988 - __main__ - INFO - Enhanced data validation: 320/320 valid examples
2025-05-29 14:05:20,988 - __main__ - INFO - Initializing tokenizer...
2025-05-29 14:05:21,549 - __main__ - INFO - Initializing enhanced model for supervised learning...
2025-05-29 14:05:23,905 - __main__ - INFO - Enhanced model initialized with 328,636,311 parameters
2025-05-29 14:05:23,905 - __main__ - INFO -   Base LLM: 124,439,808
2025-05-29 14:05:23,906 - __main__ - INFO -   Enhanced ThinkerModule: 175,024,724
2025-05-29 14:05:23,906 - __main__ - INFO -   Enhanced Decision Mechanism: 12,637,507
2025-05-29 14:05:23,906 - __main__ - INFO -   Integration Components: 16,534,272
2025-05-29 14:05:23,906 - __main__ - INFO - Creating enhanced data loaders with supervision...
2025-05-29 14:05:23,911 - __main__ - INFO - Training batches: 160
2025-05-29 14:05:23,911 - __main__ - INFO - Evaluation batches: 40
2025-05-29 14:05:23,911 - __main__ - INFO - Initializing enhanced trainer with supervision...
2025-05-29 14:05:24,776 - __main__ - INFO - Supervised Reasoning Training Configuration:
2025-05-29 14:05:24,777 - __main__ - INFO -   Batch size: 2
2025-05-29 14:05:24,777 - __main__ - INFO -   Gradient accumulation: 4
2025-05-29 14:05:24,777 - __main__ - INFO -   Learning rate: 3e-05
2025-05-29 14:05:24,777 - __main__ - INFO -   Number of epochs: 8
2025-05-29 14:05:24,777 - __main__ - INFO -   Supervised loss weights:
2025-05-29 14:05:24,777 - __main__ - INFO -     LLM: 1.0
2025-05-29 14:05:24,777 - __main__ - INFO -     Thinker (supervised): 0.5
2025-05-29 14:05:24,777 - __main__ - INFO -     Decision: 0.3
2025-05-29 14:05:24,777 - __main__ - INFO -     Projection: 0.1
2025-05-29 14:05:24,777 - __main__ - INFO - 
================================================================================
2025-05-29 14:05:24,777 - __main__ - INFO - PHASE 1: SUPERVISED REASONING TRAINING
2025-05-29 14:05:24,777 - __main__ - INFO - ================================================================================
2025-05-29 14:05:24,777 - src.training.enhanced_trainer - INFO - Starting enhanced training...
2025-05-29 14:06:04,817 - src.training.enhanced_trainer - INFO - Epoch 1/8
2025-05-29 14:06:04,817 - src.training.enhanced_trainer - INFO -   Train Loss: 1050.1997
2025-05-29 14:06:04,817 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.9641
2025-05-29 14:06:14,392 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 14:06:37,286 - src.training.enhanced_trainer - INFO - Epoch 2/8
2025-05-29 14:06:37,286 - src.training.enhanced_trainer - INFO -   Train Loss: 0.9326
2025-05-29 14:06:37,286 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.6679
2025-05-29 14:06:47,279 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 14:07:09,727 - src.training.enhanced_trainer - INFO - Epoch 3/8
2025-05-29 14:07:09,728 - src.training.enhanced_trainer - INFO -   Train Loss: 0.7372
2025-05-29 14:07:09,728 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.5355
2025-05-29 14:07:19,670 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 14:07:42,405 - src.training.enhanced_trainer - INFO - Epoch 4/8
2025-05-29 14:07:42,406 - src.training.enhanced_trainer - INFO -   Train Loss: 0.6209
2025-05-29 14:07:42,406 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.4436
2025-05-29 14:07:52,406 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 14:08:15,300 - src.training.enhanced_trainer - INFO - Epoch 5/8
2025-05-29 14:08:15,300 - src.training.enhanced_trainer - INFO -   Train Loss: 0.5479
2025-05-29 14:08:15,300 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.3895
2025-05-29 14:08:25,226 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 14:08:48,428 - src.training.enhanced_trainer - INFO - Epoch 6/8
2025-05-29 14:08:48,428 - src.training.enhanced_trainer - INFO -   Train Loss: 0.5059
2025-05-29 14:08:48,428 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.3679
2025-05-29 14:08:57,801 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 14:09:20,570 - src.training.enhanced_trainer - INFO - Epoch 7/8
2025-05-29 14:09:20,570 - src.training.enhanced_trainer - INFO -   Train Loss: 0.4862
2025-05-29 14:09:20,570 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.3508
2025-05-29 14:09:32,900 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 14:09:55,773 - src.training.enhanced_trainer - INFO - Epoch 8/8
2025-05-29 14:09:55,773 - src.training.enhanced_trainer - INFO -   Train Loss: 0.4902
2025-05-29 14:09:55,773 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.3607
2025-05-29 14:09:55,773 - src.training.enhanced_trainer - INFO - Training completed!
2025-05-29 14:09:55,773 - __main__ - INFO - Phase 1 completed successfully!
2025-05-29 14:10:07,448 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/supervised_reasoning_model.pt
2025-05-29 14:10:07,448 - __main__ - INFO - Supervised reasoning model saved
2025-05-29 14:10:07,448 - __main__ - INFO - 
================================================================================
2025-05-29 14:10:07,448 - __main__ - INFO - PHASE 2: CURRICULUM LEARNING
2025-05-29 14:10:07,448 - __main__ - INFO - ================================================================================
2025-05-29 14:10:07,451 - src.training.enhanced_trainer - INFO - Starting curriculum learning training...
2025-05-29 14:10:07,451 - src.training.enhanced_trainer - INFO - 
============================================================
2025-05-29 14:10:07,451 - src.training.enhanced_trainer - INFO - CURRICULUM STAGE 1/3
2025-05-29 14:10:07,452 - src.training.enhanced_trainer - INFO - ============================================================
2025-05-29 14:10:12,975 - src.training.enhanced_trainer - INFO - Stage 1, Epoch 1/3
2025-05-29 14:10:12,975 - src.training.enhanced_trainer - INFO -   Train Loss: 0.2270
2025-05-29 14:10:12,975 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2563
2025-05-29 14:10:23,119 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_curriculum_stage_0.pt
2025-05-29 14:10:28,421 - src.training.enhanced_trainer - INFO - Stage 1, Epoch 2/3
2025-05-29 14:10:28,421 - src.training.enhanced_trainer - INFO -   Train Loss: 0.1802
2025-05-29 14:10:28,421 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2797
2025-05-29 14:10:33,490 - src.training.enhanced_trainer - INFO - Stage 1, Epoch 3/3
2025-05-29 14:10:33,491 - src.training.enhanced_trainer - INFO -   Train Loss: 0.1501
2025-05-29 14:10:33,491 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2881
2025-05-29 14:10:33,491 - src.training.enhanced_trainer - INFO - Completed curriculum stage 1
2025-05-29 14:10:33,491 - src.training.enhanced_trainer - INFO - 
============================================================
2025-05-29 14:10:33,491 - src.training.enhanced_trainer - INFO - CURRICULUM STAGE 2/3
2025-05-29 14:10:33,491 - src.training.enhanced_trainer - INFO - ============================================================
2025-05-29 14:10:48,527 - src.training.enhanced_trainer - INFO - Stage 2, Epoch 1/3
2025-05-29 14:10:48,527 - src.training.enhanced_trainer - INFO -   Train Loss: 0.3549
2025-05-29 14:10:48,528 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2908
2025-05-29 14:11:03,497 - src.training.enhanced_trainer - INFO - Stage 2, Epoch 2/3
2025-05-29 14:11:03,497 - src.training.enhanced_trainer - INFO -   Train Loss: 0.3298
2025-05-29 14:11:03,497 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2823
2025-05-29 14:11:18,180 - src.training.enhanced_trainer - INFO - Stage 2, Epoch 3/3
2025-05-29 14:11:18,180 - src.training.enhanced_trainer - INFO -   Train Loss: 0.3215
2025-05-29 14:11:18,181 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2981
2025-05-29 14:11:18,181 - src.training.enhanced_trainer - INFO - Completed curriculum stage 2
2025-05-29 14:11:18,181 - src.training.enhanced_trainer - INFO - 
============================================================
2025-05-29 14:11:18,181 - src.training.enhanced_trainer - INFO - CURRICULUM STAGE 3/3
2025-05-29 14:11:18,181 - src.training.enhanced_trainer - INFO - ============================================================
2025-05-29 14:11:40,850 - src.training.enhanced_trainer - INFO - Stage 3, Epoch 1/3
2025-05-29 14:11:40,850 - src.training.enhanced_trainer - INFO -   Train Loss: 0.3941
2025-05-29 14:11:40,850 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2642
2025-05-29 14:12:02,841 - src.training.enhanced_trainer - INFO - Stage 3, Epoch 2/3
2025-05-29 14:12:02,843 - src.training.enhanced_trainer - INFO -   Train Loss: 0.3615
2025-05-29 14:12:02,843 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2365
2025-05-29 14:12:12,086 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_curriculum_stage_2.pt
2025-05-29 14:12:35,213 - src.training.enhanced_trainer - INFO - Stage 3, Epoch 3/3
2025-05-29 14:12:35,213 - src.training.enhanced_trainer - INFO -   Train Loss: 0.3782
2025-05-29 14:12:35,214 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.2566
2025-05-29 14:12:35,214 - src.training.enhanced_trainer - INFO - Completed curriculum stage 3
2025-05-29 14:12:44,205 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/final_curriculum_model.pt
2025-05-29 14:12:44,205 - src.training.enhanced_trainer - INFO - Curriculum learning completed!
2025-05-29 14:12:44,206 - __main__ - INFO - Phase 2 (Curriculum Learning) completed successfully!
2025-05-29 14:12:44,206 - __main__ - INFO - 
================================================================================
2025-05-29 14:12:44,206 - __main__ - INFO - PHASE 3: REASONING-ONLY FINE-TUNING
2025-05-29 14:12:44,206 - __main__ - INFO - ================================================================================
2025-05-29 14:12:44,207 - src.training.enhanced_trainer - INFO - Training mode: Reasoning components only
2025-05-29 14:12:44,208 - src.training.enhanced_trainer - INFO - Starting enhanced training...
2025-05-29 14:13:04,745 - src.training.enhanced_trainer - INFO - Epoch 1/2
2025-05-29 14:13:04,745 - src.training.enhanced_trainer - INFO -   Train Loss: 0.1690
2025-05-29 14:13:04,745 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.1090
2025-05-29 14:13:12,203 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/best_enhanced_model.pt
2025-05-29 14:13:33,550 - src.training.enhanced_trainer - INFO - Epoch 2/2
2025-05-29 14:13:33,550 - src.training.enhanced_trainer - INFO -   Train Loss: 0.1890
2025-05-29 14:13:33,550 - src.training.enhanced_trainer - INFO -   Eval Loss: 0.1227
2025-05-29 14:13:33,550 - src.training.enhanced_trainer - INFO - Training completed!
2025-05-29 14:13:33,550 - __main__ - INFO - Phase 3 (Reasoning Fine-tuning) completed successfully!
2025-05-29 14:13:40,371 - src.training.enhanced_trainer - INFO - Checkpoint saved: checkpoints/final_supervised_reasoning_model.pt
2025-05-29 14:13:40,371 - __main__ - INFO - Final supervised reasoning model saved
2025-05-29 14:13:40,371 - __main__ - INFO - 
================================================================================
2025-05-29 14:13:40,371 - __main__ - INFO - TRAINING COMPLETED - FINAL STATISTICS
2025-05-29 14:13:40,371 - __main__ - INFO - ================================================================================
2025-05-29 14:13:40,371 - __main__ - INFO -   Final epoch: 1
2025-05-29 14:13:40,371 - __main__ - INFO -   Total steps: 323
2025-05-29 14:13:40,371 - __main__ - INFO -   Best loss: 0.1090
2025-05-29 14:13:40,371 - __main__ - INFO -   Training mode: reasoning_only
2025-05-29 14:13:40,371 - __main__ - INFO - 
Key improvements implemented:
2025-05-29 14:13:40,382 - __main__ - INFO - 
Supervised reasoning training pipeline completed!
