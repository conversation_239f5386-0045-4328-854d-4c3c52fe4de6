2025-05-29 11:10:00,011 - __main__ - ERROR - Training failed with error: CUDA out of memory. Tried to allocate 1.53 GiB. GPU 0 has a total capacity of 11.99 GiB of which 0 bytes is free. Of the allocated memory 24.63 GiB is allocated by PyTorch, and 864.93 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
