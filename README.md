# LLM with Integrated ThinkerModule

A PyTorch implementation of an autoregressive LLM enhanced with a non-autoregressive ThinkerModule for improved reasoning capabilities.

## 🎯 Overview

This project implements a novel architecture that combines:
- **Main LLM Model**: Transformer-based autoregressive model for text generation
- **ThinkerModule**: Non-autoregressive reasoning component that processes entire sequences
- **Decision Mechanism**: Intelligent routing that determines when deeper reasoning is needed
- **Projection Layer**: Seamless integration between ThinkerModule and main LLM

### Key Features

- 🧠 **Enhanced Reasoning**: ThinkerModule provides deeper analysis for complex tasks
- ⚡ **Efficient Processing**: Direct LLM path for simple tasks, thinking only when needed
- 🔍 **Transparent Reasoning**: Visible reasoning steps for user understanding
- 🎛️ **Configurable**: Flexible thresholds and model sizes
- 📊 **Comprehensive Evaluation**: Built-in metrics for all components

## 🏗️ Architecture

```
Input → Decision Mechanism → Route Decision
                          ↓
        ┌─────────────────┴─────────────────┐
        ↓                                   ↓
   [Thinking Needed]                   [Direct Path]
        ↓                                   ↓
   ThinkerModule                        Main LLM
        ↓                                   ↓
   Reasoning Steps                      Generated Text
        ↓
   Projection Layer
        ↓
   Enhanced Main LLM
        ↓
   Final Output
```

### Workflow

1. **Input Processing**: Text is embedded and analyzed
2. **Decision Phase**: Complexity analysis determines routing
3. **Conditional Processing**:
   - **Complex tasks**: ThinkerModule → Reasoning → Projection → Enhanced LLM
   - **Simple tasks**: Direct LLM processing
4. **Output Generation**: Autoregressive text generation with optional reasoning display

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd llm-thinker-module

# Install dependencies
pip install -r requirements.txt
```

### Basic Usage

```python
import torch
from transformers import AutoTokenizer
from src.models.integrated_model import IntegratedLLMWithThinker
import yaml

# Load configuration
with open('config/model_config.yaml', 'r') as f:
    config = yaml.safe_load(f)

# Initialize model and tokenizer
model = IntegratedLLMWithThinker(config)
tokenizer = AutoTokenizer.from_pretrained('gpt2')
tokenizer.pad_token = tokenizer.eos_token

# Generate text with reasoning
prompt = "To solve the equation 2x + 5 = 13"
input_ids = tokenizer.encode(prompt, return_tensors='pt')

result = model.generate(
    input_ids=input_ids,
    max_new_tokens=50,
    return_reasoning=True
)

# Decode result
generated_text = tokenizer.decode(result['generated_ids'][0], skip_special_tokens=True)
print(f"Generated: {generated_text}")
print(f"Used thinking: {result['thinking_used'][0].item()}")
```

### Training

```bash
# Create sample data
python examples/train_model.py --create-data

# Train the model
python examples/train_model.py --config config/model_config.yaml

# Train with Weights & Biases logging
python examples/train_model.py --use-wandb
```

### Inference Demo

```bash
# Run inference demo
python examples/inference_demo.py --checkpoint checkpoints/best_model.pt

# Interactive mode
python examples/inference_demo.py --interactive
```

## 📁 Project Structure

```
├── src/
│   ├── models/
│   │   ├── llm_model.py           # Main autoregressive LLM
│   │   ├── thinker_module.py      # Non-autoregressive reasoning module
│   │   ├── decision_mechanism.py  # Complexity analysis and routing
│   │   ├── projection_layer.py    # State projection and integration
│   │   └── integrated_model.py    # Combined architecture
│   ├── training/
│   │   ├── trainer.py             # Training orchestration
│   │   ├── loss_functions.py      # Component-specific losses
│   │   └── data_loader.py         # Data handling utilities
│   └── evaluation/
│       ├── metrics.py             # Evaluation metrics
│       └── evaluator.py           # Model evaluation
├── examples/
│   ├── train_model.py             # Training script
│   └── inference_demo.py          # Inference demonstration
├── config/
│   └── model_config.yaml          # Model configuration
└── requirements.txt               # Dependencies
```

## ⚙️ Configuration

The model is highly configurable through `config/model_config.yaml`:

```yaml
model:
  llm:
    hidden_size: 768
    num_layers: 12
    num_attention_heads: 12
    # ... other LLM parameters
  
  thinker:
    hidden_size: 512
    num_layers: 6
    max_reasoning_steps: 8
    # ... other ThinkerModule parameters
  
  decision:
    threshold: 0.5
    complexity_features: 4
    # ... other decision parameters

training:
  batch_size: 8
  learning_rate: 5e-5
  llm_loss_weight: 1.0
  thinker_loss_weight: 0.5
  decision_loss_weight: 0.3
  # ... other training parameters
```

## 🧪 Training Strategies

### 1. Joint Training
Train all components simultaneously:
```yaml
training:
  training_mode: "joint"
```

### 2. Phased Training
Train components in phases:
```yaml
training:
  training_mode: "phased"
```

### 3. Component-Specific Training
Train specific components with different learning rates:
```yaml
training:
  training_mode: "component"
  llm_lr: 1e-5
  thinker_lr: 5e-5
  decision_lr: 1e-4
```

## 📊 Evaluation Metrics

The framework includes comprehensive evaluation metrics:

- **Perplexity**: Language modeling quality
- **Reasoning Quality**: Coherence and relevance of reasoning steps
- **Decision Accuracy**: Correctness of thinking/no-thinking decisions
- **Efficiency**: Processing time and resource usage

## 🔧 Advanced Features

### Custom Projection Strategies
- **Adaptive Projection**: Attention-based state mapping
- **Gated Projection**: Learnable gating for information flow
- **Residual Projection**: Multi-layer projection with residual connections

### Decision Mechanisms
- **Complexity Analysis**: Multi-feature input analysis
- **Confidence Estimation**: Decision confidence scoring
- **Threshold Adaptation**: Dynamic threshold adjustment

### Reasoning Generation
- **Step-by-Step Reasoning**: Visible reasoning process
- **Attention Visualization**: Focus areas for each reasoning step
- **Coherence Scoring**: Automatic reasoning quality assessment

## 📈 Performance Optimization

### Memory Efficiency
- ThinkerModule runs once per input (non-autoregressive)
- Selective activation based on complexity
- Gradient checkpointing for large models

### Computational Efficiency
- Early routing for simple tasks
- Parallel processing where possible
- Optimized attention mechanisms

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with PyTorch and Transformers
- Inspired by recent advances in reasoning-enhanced language models
- Thanks to the open-source community for foundational tools

## 📚 Citation

If you use this work in your research, please cite:

```bibtex
@misc{llm-thinker-module,
  title={LLM with Integrated ThinkerModule: Enhanced Reasoning through Non-Autoregressive Processing},
  author={Your Name},
  year={2024},
  url={https://github.com/your-username/llm-thinker-module}
}
```
