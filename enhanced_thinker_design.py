"""
Enhanced ThinkerModule Design for Complex Problem Solving
This implements a more sophisticated reasoning architecture.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Optional, Tuple, Any
import math


class PositionalEncoding(nn.Module):
    """Positional encoding for reasoning steps."""
    
    def __init__(self, d_model: int, max_len: int = 50):
        super().__init__()
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe.unsqueeze(0))
    
    def forward(self, x):
        return x + self.pe[:, :x.size(1)]


class ReasoningAttention(nn.Module):
    """Multi-head attention for reasoning steps."""
    
    def __init__(self, d_model: int, num_heads: int = 8, dropout: float = 0.1):
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        
        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        self.out_linear = nn.Linear(d_model, d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, query, key, value, mask=None):
        batch_size = query.size(0)
        
        # Linear transformations
        Q = self.q_linear(query).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        K = self.k_linear(key).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        V = self.v_linear(value).view(batch_size, -1, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Attention
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        context = torch.matmul(attention_weights, V)
        context = context.transpose(1, 2).contiguous().view(
            batch_size, -1, self.d_model)
        
        return self.out_linear(context)


class ReasoningStep(nn.Module):
    """Single reasoning step with attention and feed-forward."""
    
    def __init__(self, d_model: int, num_heads: int = 8, d_ff: int = 2048, dropout: float = 0.1):
        super().__init__()
        self.self_attention = ReasoningAttention(d_model, num_heads, dropout)
        self.cross_attention = ReasoningAttention(d_model, num_heads, dropout)
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, d_ff),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_ff, d_model)
        )
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.norm3 = nn.LayerNorm(d_model)
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, context, step_mask=None, context_mask=None):
        # Self-attention on reasoning steps
        attn_output = self.self_attention(x, x, x, step_mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # Cross-attention with input context
        if context is not None:
            cross_attn_output = self.cross_attention(x, context, context, context_mask)
            x = self.norm2(x + self.dropout(cross_attn_output))
        
        # Feed-forward
        ff_output = self.feed_forward(x)
        x = self.norm3(x + self.dropout(ff_output))
        
        return x


class ComplexityAnalyzer(nn.Module):
    """Analyzes input complexity to determine reasoning depth."""
    
    def __init__(self, d_model: int, vocab_size: int):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, d_model)
        self.encoder = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(d_model, nhead=8, batch_first=True),
            num_layers=2
        )
        self.complexity_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, input_ids, attention_mask=None):
        # Embed and encode input
        x = self.embedding(input_ids)
        x = self.encoder(x, src_key_padding_mask=~attention_mask.bool() if attention_mask is not None else None)
        
        # Pool and predict complexity
        if attention_mask is not None:
            # Masked average pooling
            mask_expanded = attention_mask.unsqueeze(-1).expand_as(x)
            x_masked = x * mask_expanded
            pooled = x_masked.sum(dim=1) / attention_mask.sum(dim=1, keepdim=True)
        else:
            pooled = x.mean(dim=1)
        
        complexity_score = self.complexity_head(pooled)
        return complexity_score


class EnhancedThinkerModule(nn.Module):
    """Enhanced ThinkerModule with sophisticated reasoning capabilities."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.d_model = config.get('d_model', 768)
        self.vocab_size = config.get('vocab_size', 50257)
        self.max_reasoning_steps = config.get('max_reasoning_steps', 16)
        self.num_reasoning_layers = config.get('num_reasoning_layers', 6)
        
        # Core components
        self.embedding = nn.Embedding(self.vocab_size, self.d_model)
        self.positional_encoding = PositionalEncoding(self.d_model, self.max_reasoning_steps)
        
        # Reasoning layers
        self.reasoning_layers = nn.ModuleList([
            ReasoningStep(self.d_model) for _ in range(self.num_reasoning_layers)
        ])
        
        # Output projection
        self.output_projection = nn.Linear(self.d_model, self.vocab_size)
        
        # Stopping mechanism
        self.stop_predictor = nn.Sequential(
            nn.Linear(self.d_model, self.d_model // 2),
            nn.ReLU(),
            nn.Linear(self.d_model // 2, 1),
            nn.Sigmoid()
        )
        
        # Complexity analyzer
        self.complexity_analyzer = ComplexityAnalyzer(self.d_model, self.vocab_size)
        
        # Quality assessor
        self.quality_assessor = nn.Sequential(
            nn.Linear(self.d_model, self.d_model // 2),
            nn.ReLU(),
            nn.Linear(self.d_model // 2, 1),
            nn.Sigmoid()
        )
        
    def analyze_complexity(self, input_ids, attention_mask=None):
        """Analyze input complexity to determine reasoning approach."""
        complexity_score = self.complexity_analyzer(input_ids, attention_mask)
        
        # Determine number of reasoning steps based on complexity
        min_steps = 2
        max_steps = self.max_reasoning_steps
        num_steps = min_steps + int((max_steps - min_steps) * complexity_score.item())
        
        return {
            'complexity_score': complexity_score,
            'suggested_steps': num_steps,
            'use_reasoning': complexity_score > 0.3  # Threshold for using reasoning
        }
    
    def generate_reasoning_step(self, step_idx, context_embedding, previous_steps=None):
        """Generate a single reasoning step."""
        batch_size = context_embedding.size(0)
        device = context_embedding.device
        
        if previous_steps is None:
            # Initialize first step
            step_embedding = torch.zeros(batch_size, 1, self.d_model, device=device)
        else:
            # Use previous steps as context
            step_embedding = previous_steps
        
        # Add positional encoding
        step_embedding = self.positional_encoding(step_embedding)
        
        # Apply reasoning layers
        for layer in self.reasoning_layers:
            step_embedding = layer(step_embedding, context_embedding)
        
        # Generate step tokens
        step_logits = self.output_projection(step_embedding[:, -1:])  # Last step only
        
        # Predict if we should stop
        stop_prob = self.stop_predictor(step_embedding[:, -1])
        
        # Assess quality of current reasoning
        quality_score = self.quality_assessor(step_embedding[:, -1])
        
        return {
            'step_logits': step_logits,
            'step_embedding': step_embedding,
            'stop_probability': stop_prob,
            'quality_score': quality_score
        }
    
    def forward(self, input_ids, attention_mask=None, max_new_steps=None):
        """Forward pass with dynamic reasoning generation."""
        batch_size = input_ids.size(0)
        device = input_ids.device
        
        # Analyze complexity
        complexity_info = self.analyze_complexity(input_ids, attention_mask)
        
        if not complexity_info['use_reasoning']:
            # Skip reasoning for simple inputs
            return {
                'reasoning_steps': [],
                'stop_probabilities': [],
                'quality_scores': [],
                'complexity_info': complexity_info,
                'used_reasoning': False
            }
        
        # Embed input context
        context_embedding = self.embedding(input_ids)
        
        # Generate reasoning steps
        reasoning_steps = []
        stop_probabilities = []
        quality_scores = []
        current_steps = None
        
        max_steps = max_new_steps or complexity_info['suggested_steps']
        
        for step_idx in range(max_steps):
            step_output = self.generate_reasoning_step(
                step_idx, context_embedding, current_steps
            )
            
            reasoning_steps.append(step_output['step_logits'])
            stop_probabilities.append(step_output['stop_probability'])
            quality_scores.append(step_output['quality_score'])
            
            # Update current steps
            if current_steps is None:
                current_steps = step_output['step_embedding']
            else:
                current_steps = torch.cat([current_steps, step_output['step_embedding'][:, -1:]], dim=1)
            
            # Check stopping condition
            if step_output['stop_probability'].mean() > 0.7:  # Stop threshold
                break
            
            # Quality-based early stopping
            if step_output['quality_score'].mean() < 0.3:  # Quality threshold
                break
        
        return {
            'reasoning_steps': reasoning_steps,
            'stop_probabilities': stop_probabilities,
            'quality_scores': quality_scores,
            'complexity_info': complexity_info,
            'used_reasoning': True,
            'final_reasoning_embedding': current_steps
        }


class EnhancedDecisionMechanism(nn.Module):
    """Enhanced decision mechanism with better complexity analysis."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.d_model = config.get('d_model', 768)
        self.vocab_size = config.get('vocab_size', 50257)
        
        # Feature extractors
        self.length_analyzer = nn.Linear(1, 64)
        self.token_diversity_analyzer = nn.Linear(1, 64)
        self.semantic_analyzer = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(self.d_model, nhead=8, batch_first=True),
            num_layers=2
        )
        
        # Decision network
        self.decision_network = nn.Sequential(
            nn.Linear(self.d_model + 128, 512),  # semantic + length + diversity features
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
        
        # Confidence estimator
        self.confidence_estimator = nn.Sequential(
            nn.Linear(self.d_model + 128, 256),
            nn.ReLU(),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
    
    def extract_features(self, input_ids, attention_mask=None, input_embeddings=None):
        """Extract comprehensive features for decision making."""
        batch_size = input_ids.size(0)
        seq_len = input_ids.size(1)
        
        # Length features
        if attention_mask is not None:
            actual_lengths = attention_mask.sum(dim=1).float()
        else:
            actual_lengths = torch.full((batch_size,), seq_len, dtype=torch.float, device=input_ids.device)
        
        length_features = self.length_analyzer(actual_lengths.unsqueeze(-1))
        
        # Token diversity features
        unique_tokens = []
        for i in range(batch_size):
            if attention_mask is not None:
                valid_tokens = input_ids[i, attention_mask[i].bool()]
            else:
                valid_tokens = input_ids[i]
            diversity = len(torch.unique(valid_tokens)) / len(valid_tokens)
            unique_tokens.append(diversity)
        
        diversity_tensor = torch.tensor(unique_tokens, device=input_ids.device).unsqueeze(-1)
        diversity_features = self.token_diversity_analyzer(diversity_tensor)
        
        # Semantic features
        if input_embeddings is not None:
            semantic_features = self.semantic_analyzer(input_embeddings)
            if attention_mask is not None:
                # Masked average pooling
                mask_expanded = attention_mask.unsqueeze(-1).expand_as(semantic_features)
                semantic_features_masked = semantic_features * mask_expanded
                semantic_pooled = semantic_features_masked.sum(dim=1) / attention_mask.sum(dim=1, keepdim=True)
            else:
                semantic_pooled = semantic_features.mean(dim=1)
        else:
            semantic_pooled = torch.zeros(batch_size, self.d_model, device=input_ids.device)
        
        # Combine features
        combined_features = torch.cat([semantic_pooled, length_features, diversity_features], dim=-1)
        
        return combined_features
    
    def forward(self, input_ids, attention_mask=None, input_embeddings=None):
        """Make decision whether to use reasoning."""
        features = self.extract_features(input_ids, attention_mask, input_embeddings)
        
        # Decision probability
        decision_prob = self.decision_network(features)
        
        # Confidence in decision
        confidence = self.confidence_estimator(features)
        
        return {
            'decision_probs': decision_prob,
            'confidence': confidence,
            'features': features
        }
